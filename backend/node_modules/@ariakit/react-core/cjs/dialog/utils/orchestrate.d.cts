export declare function orchestrate(element: Element, key: string, setup: () => () => void): () => void;
export declare function setAttribute(element: Element, attr: string, value: string): () => void;
export declare function setProperty<T extends Element, K extends keyof T & string>(element: T, property: K, value: T[K]): () => void;
export declare function assignStyle(element: HTMLElement | null | undefined, style: Partial<CSSStyleDeclaration>): () => void;
export declare function setCSSProperty(element: HTMLElement | null | undefined, property: string, value: string): () => void;
