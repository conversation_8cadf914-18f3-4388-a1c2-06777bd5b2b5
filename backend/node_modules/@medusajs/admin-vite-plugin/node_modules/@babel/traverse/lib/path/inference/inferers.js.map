{"version": 3, "names": ["_t", "require", "_infererReference", "_util", "BOOLEAN_BINARY_OPERATORS", "BOOLEAN_UNARY_OPERATORS", "NUMBER_BINARY_OPERATORS", "NUMBER_UNARY_OPERATORS", "STRING_UNARY_OPERATORS", "anyTypeAnnotation", "arrayTypeAnnotation", "booleanTypeAnnotation", "buildMatchMemberExpression", "genericTypeAnnotation", "identifier", "nullLiteralTypeAnnotation", "numberTypeAnnotation", "stringTypeAnnotation", "tupleTypeAnnotation", "unionTypeAnnotation", "voidTypeAnnotation", "isIdentifier", "VariableDeclarator", "get", "getTypeAnnotation", "TypeCastExpression", "node", "typeAnnotation", "validParent", "TSAsExpression", "TSNonNullExpression", "NewExpression", "callee", "type", "TemplateLiteral", "UnaryExpression", "operator", "includes", "BinaryExpression", "right", "left", "isBaseType", "LogicalExpression", "argumentTypes", "createUnionType", "ConditionalExpression", "SequenceExpression", "pop", "ParenthesizedExpression", "AssignmentExpression", "UpdateExpression", "StringLiteral", "NumericLiteral", "<PERSON>olean<PERSON>iter<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RegExpLiteral", "ObjectExpression", "ArrayExpression", "RestElement", "Func", "isArrayFrom", "isObjectKeys", "isObjectValues", "isObjectEntries", "CallExpression", "name", "resolveCall", "TaggedTemplateExpression", "resolve", "isFunction", "async", "generator", "returnType"], "sources": ["../../../src/path/inference/inferers.ts"], "sourcesContent": ["import {\n  BOOLEAN_BINARY_OPERATORS,\n  BOOLEAN_UNARY_OPERATORS,\n  NUMBER_BINARY_OPERATORS,\n  NUMBER_UNARY_OPERATORS,\n  STRING_UNARY_OPERATORS,\n  anyTypeAnnotation,\n  arrayTypeAnnotation,\n  booleanTypeAnnotation,\n  buildMatchMemberExpression,\n  genericTypeAnnotation,\n  identifier,\n  nullLiteralTypeAnnotation,\n  numberTypeAnnotation,\n  stringTypeAnnotation,\n  tupleTypeAnnotation,\n  unionTypeAnnotation,\n  voidTypeAnnotation,\n  isIdentifier,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport { default as Identifier } from \"./inferer-reference.ts\";\n\nimport { createUnionType } from \"./util.ts\";\nimport type NodePath from \"../index.ts\";\n\nexport function VariableDeclarator(this: NodePath<t.VariableDeclarator>) {\n  if (!this.get(\"id\").isIdentifier()) return;\n  return this.get(\"init\").getTypeAnnotation();\n}\n\nexport function TypeCastExpression(node: t.TypeCastExpression) {\n  return node.typeAnnotation;\n}\n\nTypeCastExpression.validParent = true;\n\nexport function TSAsExpression(node: t.TSAsExpression) {\n  return node.typeAnnotation;\n}\n\nTSAsExpression.validParent = true;\n\nexport function TSNonNullExpression(this: NodePath<t.TSNonNullExpression>) {\n  return this.get(\"expression\").getTypeAnnotation();\n}\n\nexport function NewExpression(\n  this: NodePath<t.NewExpression>,\n  node: t.NewExpression,\n) {\n  if (node.callee.type === \"Identifier\") {\n    // only resolve identifier callee\n    return genericTypeAnnotation(node.callee);\n  }\n}\n\nexport function TemplateLiteral() {\n  return stringTypeAnnotation();\n}\n\nexport function UnaryExpression(node: t.UnaryExpression) {\n  const operator = node.operator;\n\n  if (operator === \"void\") {\n    return voidTypeAnnotation();\n  } else if (NUMBER_UNARY_OPERATORS.includes(operator)) {\n    return numberTypeAnnotation();\n  } else if (STRING_UNARY_OPERATORS.includes(operator)) {\n    return stringTypeAnnotation();\n  } else if (BOOLEAN_UNARY_OPERATORS.includes(operator)) {\n    return booleanTypeAnnotation();\n  }\n}\n\nexport function BinaryExpression(\n  this: NodePath<t.BinaryExpression>,\n  node: t.BinaryExpression,\n) {\n  const operator = node.operator;\n\n  if (NUMBER_BINARY_OPERATORS.includes(operator)) {\n    return numberTypeAnnotation();\n  } else if (BOOLEAN_BINARY_OPERATORS.includes(operator)) {\n    return booleanTypeAnnotation();\n  } else if (operator === \"+\") {\n    const right = this.get(\"right\");\n    const left = this.get(\"left\");\n\n    if (left.isBaseType(\"number\") && right.isBaseType(\"number\")) {\n      // both numbers so this will be a number\n      return numberTypeAnnotation();\n    } else if (left.isBaseType(\"string\") || right.isBaseType(\"string\")) {\n      // one is a string so the result will be a string\n      return stringTypeAnnotation();\n    }\n\n    // unsure if left and right are strings or numbers so stay on the safe side\n    return unionTypeAnnotation([\n      stringTypeAnnotation(),\n      numberTypeAnnotation(),\n    ]);\n  }\n}\n\nexport function LogicalExpression(this: NodePath<t.LogicalExpression>) {\n  const argumentTypes = [\n    this.get(\"left\").getTypeAnnotation(),\n    this.get(\"right\").getTypeAnnotation(),\n  ];\n\n  return createUnionType(argumentTypes);\n}\n\nexport function ConditionalExpression(this: NodePath<t.ConditionalExpression>) {\n  const argumentTypes = [\n    this.get(\"consequent\").getTypeAnnotation(),\n    this.get(\"alternate\").getTypeAnnotation(),\n  ];\n\n  return createUnionType(argumentTypes);\n}\n\nexport function SequenceExpression(this: NodePath<t.SequenceExpression>) {\n  return this.get(\"expressions\").pop().getTypeAnnotation();\n}\n\nexport function ParenthesizedExpression(\n  this: NodePath<t.ParenthesizedExpression>,\n) {\n  return this.get(\"expression\").getTypeAnnotation();\n}\n\nexport function AssignmentExpression(this: NodePath<t.AssignmentExpression>) {\n  return this.get(\"right\").getTypeAnnotation();\n}\n\nexport function UpdateExpression(\n  this: NodePath<t.UpdateExpression>,\n  node: t.UpdateExpression,\n) {\n  const operator = node.operator;\n  if (operator === \"++\" || operator === \"--\") {\n    return numberTypeAnnotation();\n  }\n}\n\nexport function StringLiteral() {\n  return stringTypeAnnotation();\n}\n\nexport function NumericLiteral() {\n  return numberTypeAnnotation();\n}\n\nexport function BooleanLiteral() {\n  return booleanTypeAnnotation();\n}\n\nexport function NullLiteral() {\n  return nullLiteralTypeAnnotation();\n}\n\nexport function RegExpLiteral() {\n  return genericTypeAnnotation(identifier(\"RegExp\"));\n}\n\nexport function ObjectExpression() {\n  return genericTypeAnnotation(identifier(\"Object\"));\n}\n\nexport function ArrayExpression() {\n  return genericTypeAnnotation(identifier(\"Array\"));\n}\n\nexport function RestElement() {\n  return ArrayExpression();\n}\n\nRestElement.validParent = true;\n\nfunction Func() {\n  return genericTypeAnnotation(identifier(\"Function\"));\n}\n\nexport {\n  Func as FunctionExpression,\n  Func as ArrowFunctionExpression,\n  Func as FunctionDeclaration,\n  Func as ClassExpression,\n  Func as ClassDeclaration,\n};\n\nconst isArrayFrom = buildMatchMemberExpression(\"Array.from\");\nconst isObjectKeys = buildMatchMemberExpression(\"Object.keys\");\nconst isObjectValues = buildMatchMemberExpression(\"Object.values\");\nconst isObjectEntries = buildMatchMemberExpression(\"Object.entries\");\nexport function CallExpression(this: NodePath<t.CallExpression>) {\n  const { callee } = this.node;\n  if (isObjectKeys(callee)) {\n    return arrayTypeAnnotation(stringTypeAnnotation());\n  } else if (\n    isArrayFrom(callee) ||\n    isObjectValues(callee) ||\n    // Detect \"var foo = Array()\" calls so we can optimize for arrays vs iterables.\n    isIdentifier(callee, { name: \"Array\" })\n  ) {\n    return arrayTypeAnnotation(anyTypeAnnotation());\n  } else if (isObjectEntries(callee)) {\n    return arrayTypeAnnotation(\n      tupleTypeAnnotation([stringTypeAnnotation(), anyTypeAnnotation()]),\n    );\n  }\n\n  return resolveCall(this.get(\"callee\"));\n}\n\nexport function TaggedTemplateExpression(\n  this: NodePath<t.TaggedTemplateExpression>,\n) {\n  return resolveCall(this.get(\"tag\"));\n}\n\nfunction resolveCall(callee: NodePath) {\n  callee = callee.resolve();\n\n  if (callee.isFunction()) {\n    const { node } = callee;\n    if (node.async) {\n      if (node.generator) {\n        return genericTypeAnnotation(identifier(\"AsyncIterator\"));\n      } else {\n        return genericTypeAnnotation(identifier(\"Promise\"));\n      }\n    } else {\n      if (node.generator) {\n        return genericTypeAnnotation(identifier(\"Iterator\"));\n      } else if (callee.node.returnType) {\n        return callee.node.returnType;\n      } else {\n        // todo: get union type of all return arguments\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAsBA,IAAAC,iBAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAF,OAAA;AAA4C;EAvB1CG,wBAAwB;EACxBC,uBAAuB;EACvBC,uBAAuB;EACvBC,sBAAsB;EACtBC,sBAAsB;EACtBC,iBAAiB;EACjBC,mBAAmB;EACnBC,qBAAqB;EACrBC,0BAA0B;EAC1BC,qBAAqB;EACrBC,UAAU;EACVC,yBAAyB;EACzBC,oBAAoB;EACpBC,oBAAoB;EACpBC,mBAAmB;EACnBC,mBAAmB;EACnBC,kBAAkB;EAClBC;AAAY,IAAArB,EAAA;AASP,SAASsB,kBAAkBA,CAAA,EAAuC;EACvE,IAAI,CAAC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC,CAACF,YAAY,CAAC,CAAC,EAAE;EACpC,OAAO,IAAI,CAACE,GAAG,CAAC,MAAM,CAAC,CAACC,iBAAiB,CAAC,CAAC;AAC7C;AAEO,SAASC,kBAAkBA,CAACC,IAA0B,EAAE;EAC7D,OAAOA,IAAI,CAACC,cAAc;AAC5B;AAEAF,kBAAkB,CAACG,WAAW,GAAG,IAAI;AAE9B,SAASC,cAAcA,CAACH,IAAsB,EAAE;EACrD,OAAOA,IAAI,CAACC,cAAc;AAC5B;AAEAE,cAAc,CAACD,WAAW,GAAG,IAAI;AAE1B,SAASE,mBAAmBA,CAAA,EAAwC;EACzE,OAAO,IAAI,CAACP,GAAG,CAAC,YAAY,CAAC,CAACC,iBAAiB,CAAC,CAAC;AACnD;AAEO,SAASO,aAAaA,CAE3BL,IAAqB,EACrB;EACA,IAAIA,IAAI,CAACM,MAAM,CAACC,IAAI,KAAK,YAAY,EAAE;IAErC,OAAOpB,qBAAqB,CAACa,IAAI,CAACM,MAAM,CAAC;EAC3C;AACF;AAEO,SAASE,eAAeA,CAAA,EAAG;EAChC,OAAOjB,oBAAoB,CAAC,CAAC;AAC/B;AAEO,SAASkB,eAAeA,CAACT,IAAuB,EAAE;EACvD,MAAMU,QAAQ,GAAGV,IAAI,CAACU,QAAQ;EAE9B,IAAIA,QAAQ,KAAK,MAAM,EAAE;IACvB,OAAOhB,kBAAkB,CAAC,CAAC;EAC7B,CAAC,MAAM,IAAIb,sBAAsB,CAAC8B,QAAQ,CAACD,QAAQ,CAAC,EAAE;IACpD,OAAOpB,oBAAoB,CAAC,CAAC;EAC/B,CAAC,MAAM,IAAIR,sBAAsB,CAAC6B,QAAQ,CAACD,QAAQ,CAAC,EAAE;IACpD,OAAOnB,oBAAoB,CAAC,CAAC;EAC/B,CAAC,MAAM,IAAIZ,uBAAuB,CAACgC,QAAQ,CAACD,QAAQ,CAAC,EAAE;IACrD,OAAOzB,qBAAqB,CAAC,CAAC;EAChC;AACF;AAEO,SAAS2B,gBAAgBA,CAE9BZ,IAAwB,EACxB;EACA,MAAMU,QAAQ,GAAGV,IAAI,CAACU,QAAQ;EAE9B,IAAI9B,uBAAuB,CAAC+B,QAAQ,CAACD,QAAQ,CAAC,EAAE;IAC9C,OAAOpB,oBAAoB,CAAC,CAAC;EAC/B,CAAC,MAAM,IAAIZ,wBAAwB,CAACiC,QAAQ,CAACD,QAAQ,CAAC,EAAE;IACtD,OAAOzB,qBAAqB,CAAC,CAAC;EAChC,CAAC,MAAM,IAAIyB,QAAQ,KAAK,GAAG,EAAE;IAC3B,MAAMG,KAAK,GAAG,IAAI,CAAChB,GAAG,CAAC,OAAO,CAAC;IAC/B,MAAMiB,IAAI,GAAG,IAAI,CAACjB,GAAG,CAAC,MAAM,CAAC;IAE7B,IAAIiB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAE;MAE3D,OAAOzB,oBAAoB,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIwB,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,IAAIF,KAAK,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAE;MAElE,OAAOxB,oBAAoB,CAAC,CAAC;IAC/B;IAGA,OAAOE,mBAAmB,CAAC,CACzBF,oBAAoB,CAAC,CAAC,EACtBD,oBAAoB,CAAC,CAAC,CACvB,CAAC;EACJ;AACF;AAEO,SAAS0B,iBAAiBA,CAAA,EAAsC;EACrE,MAAMC,aAAa,GAAG,CACpB,IAAI,CAACpB,GAAG,CAAC,MAAM,CAAC,CAACC,iBAAiB,CAAC,CAAC,EACpC,IAAI,CAACD,GAAG,CAAC,OAAO,CAAC,CAACC,iBAAiB,CAAC,CAAC,CACtC;EAED,OAAO,IAAAoB,qBAAe,EAACD,aAAa,CAAC;AACvC;AAEO,SAASE,qBAAqBA,CAAA,EAA0C;EAC7E,MAAMF,aAAa,GAAG,CACpB,IAAI,CAACpB,GAAG,CAAC,YAAY,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAC1C,IAAI,CAACD,GAAG,CAAC,WAAW,CAAC,CAACC,iBAAiB,CAAC,CAAC,CAC1C;EAED,OAAO,IAAAoB,qBAAe,EAACD,aAAa,CAAC;AACvC;AAEO,SAASG,kBAAkBA,CAAA,EAAuC;EACvE,OAAO,IAAI,CAACvB,GAAG,CAAC,aAAa,CAAC,CAACwB,GAAG,CAAC,CAAC,CAACvB,iBAAiB,CAAC,CAAC;AAC1D;AAEO,SAASwB,uBAAuBA,CAAA,EAErC;EACA,OAAO,IAAI,CAACzB,GAAG,CAAC,YAAY,CAAC,CAACC,iBAAiB,CAAC,CAAC;AACnD;AAEO,SAASyB,oBAAoBA,CAAA,EAAyC;EAC3E,OAAO,IAAI,CAAC1B,GAAG,CAAC,OAAO,CAAC,CAACC,iBAAiB,CAAC,CAAC;AAC9C;AAEO,SAAS0B,gBAAgBA,CAE9BxB,IAAwB,EACxB;EACA,MAAMU,QAAQ,GAAGV,IAAI,CAACU,QAAQ;EAC9B,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,IAAI,EAAE;IAC1C,OAAOpB,oBAAoB,CAAC,CAAC;EAC/B;AACF;AAEO,SAASmC,aAAaA,CAAA,EAAG;EAC9B,OAAOlC,oBAAoB,CAAC,CAAC;AAC/B;AAEO,SAASmC,cAAcA,CAAA,EAAG;EAC/B,OAAOpC,oBAAoB,CAAC,CAAC;AAC/B;AAEO,SAASqC,cAAcA,CAAA,EAAG;EAC/B,OAAO1C,qBAAqB,CAAC,CAAC;AAChC;AAEO,SAAS2C,WAAWA,CAAA,EAAG;EAC5B,OAAOvC,yBAAyB,CAAC,CAAC;AACpC;AAEO,SAASwC,aAAaA,CAAA,EAAG;EAC9B,OAAO1C,qBAAqB,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;AACpD;AAEO,SAAS0C,gBAAgBA,CAAA,EAAG;EACjC,OAAO3C,qBAAqB,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAC;AACpD;AAEO,SAAS2C,eAAeA,CAAA,EAAG;EAChC,OAAO5C,qBAAqB,CAACC,UAAU,CAAC,OAAO,CAAC,CAAC;AACnD;AAEO,SAAS4C,WAAWA,CAAA,EAAG;EAC5B,OAAOD,eAAe,CAAC,CAAC;AAC1B;AAEAC,WAAW,CAAC9B,WAAW,GAAG,IAAI;AAE9B,SAAS+B,IAAIA,CAAA,EAAG;EACd,OAAO9C,qBAAqB,CAACC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtD;AAUA,MAAM8C,WAAW,GAAGhD,0BAA0B,CAAC,YAAY,CAAC;AAC5D,MAAMiD,YAAY,GAAGjD,0BAA0B,CAAC,aAAa,CAAC;AAC9D,MAAMkD,cAAc,GAAGlD,0BAA0B,CAAC,eAAe,CAAC;AAClE,MAAMmD,eAAe,GAAGnD,0BAA0B,CAAC,gBAAgB,CAAC;AAC7D,SAASoD,cAAcA,CAAA,EAAmC;EAC/D,MAAM;IAAEhC;EAAO,CAAC,GAAG,IAAI,CAACN,IAAI;EAC5B,IAAImC,YAAY,CAAC7B,MAAM,CAAC,EAAE;IACxB,OAAOtB,mBAAmB,CAACO,oBAAoB,CAAC,CAAC,CAAC;EACpD,CAAC,MAAM,IACL2C,WAAW,CAAC5B,MAAM,CAAC,IACnB8B,cAAc,CAAC9B,MAAM,CAAC,IAEtBX,YAAY,CAACW,MAAM,EAAE;IAAEiC,IAAI,EAAE;EAAQ,CAAC,CAAC,EACvC;IACA,OAAOvD,mBAAmB,CAACD,iBAAiB,CAAC,CAAC,CAAC;EACjD,CAAC,MAAM,IAAIsD,eAAe,CAAC/B,MAAM,CAAC,EAAE;IAClC,OAAOtB,mBAAmB,CACxBQ,mBAAmB,CAAC,CAACD,oBAAoB,CAAC,CAAC,EAAER,iBAAiB,CAAC,CAAC,CAAC,CACnE,CAAC;EACH;EAEA,OAAOyD,WAAW,CAAC,IAAI,CAAC3C,GAAG,CAAC,QAAQ,CAAC,CAAC;AACxC;AAEO,SAAS4C,wBAAwBA,CAAA,EAEtC;EACA,OAAOD,WAAW,CAAC,IAAI,CAAC3C,GAAG,CAAC,KAAK,CAAC,CAAC;AACrC;AAEA,SAAS2C,WAAWA,CAAClC,MAAgB,EAAE;EACrCA,MAAM,GAAGA,MAAM,CAACoC,OAAO,CAAC,CAAC;EAEzB,IAAIpC,MAAM,CAACqC,UAAU,CAAC,CAAC,EAAE;IACvB,MAAM;MAAE3C;IAAK,CAAC,GAAGM,MAAM;IACvB,IAAIN,IAAI,CAAC4C,KAAK,EAAE;MACd,IAAI5C,IAAI,CAAC6C,SAAS,EAAE;QAClB,OAAO1D,qBAAqB,CAACC,UAAU,CAAC,eAAe,CAAC,CAAC;MAC3D,CAAC,MAAM;QACL,OAAOD,qBAAqB,CAACC,UAAU,CAAC,SAAS,CAAC,CAAC;MACrD;IACF,CAAC,MAAM;MACL,IAAIY,IAAI,CAAC6C,SAAS,EAAE;QAClB,OAAO1D,qBAAqB,CAACC,UAAU,CAAC,UAAU,CAAC,CAAC;MACtD,CAAC,MAAM,IAAIkB,MAAM,CAACN,IAAI,CAAC8C,UAAU,EAAE;QACjC,OAAOxC,MAAM,CAACN,IAAI,CAAC8C,UAAU;MAC/B,CAAC,MAAM,CAEP;IACF;EACF;AACF", "ignoreList": []}