{"version": 3, "file": "resource-loader.js", "sourceRoot": "", "sources": ["../../src/utils/resource-loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA6E;AAE7E,0CAAoC;AACpC,+BAAkC;AAClC,sCAAkC;AAElC,MAAsB,cAAc;IAkBlC,YAAY,SAA4B;QAZxC;;;WAGG;QACH,4CAA6B;QAE7B;;;WAGG;QACH,mCAAsB,CAAC,wBAAwB,CAAC,EAAA;QAG9C,uBAAA,IAAI,6BAAc,SAAS,MAAA,CAAA;IAC7B,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,iBAAiB,CAAC,EAChC,OAAO,EACP,eAAe,MAIb,EAAE;QACJ,OAAO,KAAK,EAAE,CAAA;QACd,eAAe,KAAK,CAAC,KAAa,EAAE,EAAE;YACpC,MAAM,UAAU,GAAG,IAAA,YAAK,EAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAEpC,OAAO,CACL,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,UAAU,CAAC,IAAI,KAAK,OAAO;gBAC3B,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAClC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;gBACvC,CAAC,uBAAA,IAAI,gCAAU,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAChE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAC1D,CAAA;QACH,CAAC,CAAA;QAED,MAAM,oBAAoB,GAAG,KAAK,CAAC,OAAO,CAAC,uBAAA,IAAI,iCAAW,CAAC;YACzD,CAAC,CAAC,uBAAA,IAAI,iCAAW;YACjB,CAAC,CAAC,CAAC,uBAAA,IAAI,iCAAW,CAAC,CAAA;QAErB,MAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;YAC7D,IAAI,CAAC;gBACH,MAAM,IAAA,iBAAM,EAAC,UAAU,CAAC,CAAA;YAC1B,CAAC;YAAC,MAAM,CAAC;gBACP,eAAM,CAAC,IAAI,CACT,MAAM,IAAI,CAAC,YAAY,iBAAiB,UAAU,YAAY,CAC/D,CAAA;gBACD,OAAM;YACR,CAAC;YAED,OAAO,MAAM,IAAA,wBAAgB,EAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;gBAC/D,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAAa,EAAE,EAAE,CACnD,eAAe,CAAC,KAAK,CAAC,CACvB,CAAA;gBAED,OAAO,MAAM,IAAA,kBAAU,EACrB,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,KAAa,EAAE,EAAE;oBACtC,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;oBAE7C,MAAM,OAAO,GAAG,MAAM,IAAA,qBAAa,EAAC,QAAQ,CAAC,CAAA;oBAE7C,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;oBAC1C,OAAO,OAAO,CAAA;gBAChB,CAAC,CAAC,CACH,CAAA;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;QAEF,MAAM,SAAS,GAAG,MAAM,IAAA,kBAAU,EAAC,QAAQ,CAAC,CAAA;QAC5C,OAAO,SAAS,CAAC,IAAI,EAAE,CAAA;IACzB,CAAC;CAWF;AA9FD,wCA8FC"}