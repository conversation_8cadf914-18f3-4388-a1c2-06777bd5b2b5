{"version": 3, "file": "migrator.js", "sourceRoot": "", "sources": ["../../src/migrations/migrator.ts"], "names": [], "mappings": ";;;;;;;;;AAEA,+BAA2B;AAC3B,+BAA2B;AAC3B,sCAAkC;AAClC,oCAAoD;AAEpD,MAAsB,QAAQ;IAQ5B,YAAY,EAAE,SAAS,EAAkC;QAFzD,uCAAwC,IAAI,GAAG,EAAE,EAAA;QAG/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CACxC,iCAAyB,CAAC,aAAa,CACxC,CAAA;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,EAAE,CAAA;QAClC,OAAO;YACL,UAAU;gBACR,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;gBAC1C,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACrD,CAAC;SACF,CAAA;IACH,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CACzC,iCAAyB,CAAC,aAAa,CACxC,CAAA;QAED,IAAI,CAAC;YACH,MAAM,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,eAAM,CAAC,KAAK,CACV,0BAA0B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CACjE,CAAA;gBACD,eAAM,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAA;YAC/D,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACrB,CAAC;YACD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;;;;8BAIxB,IAAI,CAAC,oBAAoB;;OAEhD,CAAC,CAAA;YAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBAChC,eAAM,CAAC,IAAI,CACT,8BAA8B,IAAI,CAAC,oBAAoB,MAAM,CAC9D,CAAA;gBACD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;gBACjC,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAA;YACtD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAA;YAChE,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACxC,iBAAiB,IAAI,CAAC,oBAAoB,EAAE,CAC7C,CAAA;YACD,OAAO,MAAM,CAAC,IAAI,CAAA;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAA;YACzD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAA8B;QAClD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;YAC7D,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;YAEvC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,CACzB,eAAe,IAAI,CAAC,oBAAoB,KAAK,OAAO,CAAC,IAAI,CACvD,IAAI,CACL,YAAY,MAAM;iBAChB,GAAG,CACF,CAAC,UAAU,EAAE,EAAE,CACb,IAAI,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAC1D;iBACA,IAAI,CAAC,GAAG,CAAC,EAAE,EACd,MAAM,CAAC,IAAI,EAAE,CACd,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CACV,qCAAqC,IAAI,CAAC,oBAAoB,IAAI,EAClE,KAAK,CACN,CAAA;YACD,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,kBAAkB,CACtB,KAAe,EACf,EAAE,KAAK,KAA0B,EAAE,KAAK,EAAE,KAAK,EAAE;QAEjD,MAAM,UAAU,GAAa,EAAE,CAAA;QAE/B,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,IAAI,uBAAA,IAAI,oCAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrD,UAAU,CAAC,IAAI,CAAC,GAAG,uBAAA,IAAI,oCAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;gBAC1D,SAAQ;YACV,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,WAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACzC,GAAG,EAAE,QAAQ;oBACb,MAAM,EAAE,CAAC,kBAAkB,EAAE,WAAW,CAAC;iBAC1C,CAAC,CAAA;gBAEF,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;oBACzB,SAAQ;gBACV,CAAC;gBAED,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAA,WAAI,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAA;gBACrE,uBAAA,IAAI,oCAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;gBAEjD,UAAU,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAA;gBACvE,MAAM,KAAK,CAAA;YACb,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAA;IACnB,CAAC;CAKF;AA3JD,4BA2JC"}