{"version": 3, "file": "express-loader.js", "sourceRoot": "", "sources": ["../../src/http/express-loader.ts"], "names": [], "mappings": ";;;;;AAgBA,sCA0IC;AA1JD,kEAAuC;AACvC,kEAAwC;AACxC,sDAA0D;AAC1D,sEAAqC;AACrC,sDAA2B;AAC3B,oDAA2B;AAC3B,gDAAuB;AACvB,sCAAkC;AAClC,sCAAyC;AAEzC,2CAA+C;AAE/C,MAAM,sBAAsB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAA;AAEhF,MAAM,aAAa,GAAG,CAAC,GAAkB,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,CAAA;AAE7D,KAAK,UAAU,aAAa,CAAC,EAAE,GAAG,EAAoB;IAI3D,MAAM,OAAO,GAAG,sBAAa,CAAC,OAAO,CAAA;IACrC,MAAM,YAAY,GAAG,sBAAa,CAAC,MAAM,CAAA;IACzC,MAAM,YAAY,GAAG,sBAAa,CAAC,YAAY,CAAA;IAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAA;IACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IACzC,MAAM,SAAS,GAAG,QAAQ,KAAK,SAAS,CAAA;IACxC,MAAM,MAAM,GAAG,QAAQ,KAAK,MAAM,CAAA;IAElC,IAAI,QAAQ,GAAqB,KAAK,CAAA;IACtC,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,IAAI,YAAY,IAAI,SAAS,EAAE,CAAC;QAC9B,MAAM,GAAG,IAAI,CAAA;QACb,QAAQ,GAAG,MAAM,CAAA;IACnB,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,YAAY,CAAC,aAAa,CAAA;IAC3D,MAAM,WAAW,GAAG;QAClB,IAAI,EAAE,cAAc,EAAE,IAAI,IAAI,aAAa;QAC3C,MAAM,EAAE,cAAc,EAAE,MAAM,IAAI,IAAI;QACtC,OAAO,EAAE,cAAc,EAAE,OAAO,IAAI,KAAK;QACzC,iBAAiB,EAAE,cAAc,EAAE,iBAAiB,IAAI,KAAK;QAC7D,KAAK,EAAE,IAAI;QACX,MAAM,EAAE,cAAc,EAAE,MAAM,IAAI,IAAI,EAAE,YAAY;QACpD,MAAM,EAAE;YACN,QAAQ;YACR,MAAM;YACN,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;SACnD;QACD,KAAK,EAAE,IAAI;KACZ,CAAA;IAED,IAAI,WAAkB,CAAA;IAEtB,IAAI,YAAY,EAAE,aAAa,CAAC,cAAc,EAAE,eAAe,EAAE,CAAC;QAChE,MAAM,YAAY,GAAG,MAAM,IAAA,qBAAa,EAAC,kBAAkB,CAAC,CAAA;QAC5D,MAAM,MAAM,GAAG,MAAM,IAAA,qBAAa,EAAC,0BAA0B,CAAC,CAAA;QAC9D,MAAM,aAAa,GAAG,YAAY,CAAC,EAAE,OAAO,EAAP,yBAAO,EAAE,CAAC,CAAA;QAC/C,WAAW,CAAC,KAAK,GAAG,IAAI,aAAa,CAAC;YACpC,GAAG,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe;YAC5D,MAAM,EAAE,IAAI,MAAM,CAAC,cAAc,CAC/B,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CACxE;SACF,CAAC,CAAA;IACJ,CAAC;SAAM,IAAI,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC;QACjD,MAAM,UAAU,GAAG,IAAA,uBAAW,EAAC,yBAAO,CAAC,CAAA;QACvC,WAAW,GAAG,IAAI,iBAAK,CACrB,YAAY,CAAC,aAAa,CAAC,QAAQ,EACnC,YAAY,CAAC,aAAa,CAAC,YAAY,IAAI,EAAE,CAC9C,CAAA;QACD,WAAW,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC;YACjC,MAAM,EAAE,WAAW;YACnB,MAAM,EAAE,GAAG,YAAY,EAAE,aAAa,EAAE,WAAW,IAAI,EAAE,OAAO;SACjE,CAAC,CAAA;IACJ,CAAC;IAED,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC,CAAA;IAEzB;;;OAGG;IACH,SAAS,iBAAiB,CAAC,GAAkB,EAAE,GAAmB;QAChE,OAAO,CACL,MAAM;YACN,aAAa,CAAC,GAAG,CAAC;YAClB,sBAAsB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC/D,CAAC,eAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAC1B,CAAA;IACH,CAAC;IAED,IAAI,iBAAiC,CAAA;IAErC;;;OAGG;IACH,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,UAAU,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG;gBACb,KAAK,EAAE,MAAM;gBACb,YAAY;gBACZ,SAAS,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG;gBAExB,oEAAoE;gBACpE,UAAU,EAAE,GAAG,CAAC,SAAS,IAAI,GAAG;gBAEhC,mCAAmC;gBACnC,YAAY,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC9C,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;gBAC/B,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;gBAE1B,mBAAmB;gBACnB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC;gBAC1D,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,gBAAgB,CAAC,IAAI,CAAC;gBACzD,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAEnD,yDAAyD;gBACzD,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,GAAG;gBAC1C,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;gBAE1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAA;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC/B,CAAC,CAAA;QAED,iBAAiB,GAAG,IAAA,gBAAM,EAAC,UAAU,EAAE;YACrC,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAA;IACJ,CAAC;SAAM,CAAC;QACN,iBAAiB,GAAG,IAAA,gBAAM,EACxB,wDAAwD,EACxD;YACE,IAAI,EAAE,iBAAiB;YACvB,MAAM,EAAE;gBACN,KAAK,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;aACxD;SACF,CACF,CAAA;IACH,CAAC;IAED,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;IAC1B,GAAG,CAAC,GAAG,CAAC,IAAA,uBAAY,GAAE,CAAC,CAAA;IACvB,GAAG,CAAC,GAAG,CAAC,IAAA,yBAAO,EAAC,WAAW,CAAC,CAAC,CAAA;IAE7B,+FAA+F;IAC/F,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAO,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;IAEhE,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE;QAC1B,WAAW,EAAE,UAAU,EAAE,CAAA;IAC3B,CAAC,CAAA;IAED,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAA;AAC1B,CAAC"}