"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationFulfillmentProvider = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.LocationFulfillmentProvider = {
    serviceName: utils_1.LINKS.LocationFulfillmentProvider,
    isLink: true,
    databaseConfig: {
        tableName: "location_fulfillment_provider",
        idPrefix: "locfp",
    },
    alias: [
        {
            name: ["location_fulfillment_provider", "location_fulfillment_providers"],
            entity: "LinkLocationFulfillmentProvider",
        },
    ],
    primaryKeys: ["id", "stock_location_id", "fulfillment_provider_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.STOCK_LOCATION,
            entity: "StockLocation",
            primaryKey: "id",
            foreignKey: "stock_location_id",
            alias: "location",
            args: { methodSuffix: "StockLocations" },
            hasMany: true,
        },
        {
            serviceName: utils_1.Modules.FULFILLMENT,
            entity: "FulfillmentProvider",
            primaryKey: "id",
            foreignKey: "fulfillment_provider_id",
            alias: "fulfillment_provider",
            args: { methodSuffix: "FulfillmentProviders" },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.STOCK_LOCATION,
            relationship: {
                serviceName: utils_1.LINKS.LocationFulfillmentProvider,
                primaryKey: "stock_location_id",
                foreignKey: "id",
                alias: "fulfillment_provider_link",
                isList: true,
            },
            fieldAlias: {
                fulfillment_providers: {
                    path: "fulfillment_provider_link.fulfillment_provider",
                    isList: true,
                },
            },
        },
        {
            serviceName: utils_1.Modules.FULFILLMENT,
            entity: "FulfillmentProvider",
            relationship: {
                serviceName: utils_1.LINKS.LocationFulfillmentProvider,
                primaryKey: "fulfillment_provider_id",
                foreignKey: "id",
                alias: "locations_link",
                isList: true,
            },
            fieldAlias: {
                locations: {
                    path: "locations_link.location",
                    isList: true,
                },
            },
        },
    ],
};
//# sourceMappingURL=fulfillment-provider-location.js.map