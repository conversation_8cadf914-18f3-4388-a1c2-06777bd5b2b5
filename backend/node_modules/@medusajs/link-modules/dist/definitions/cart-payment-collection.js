"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartPaymentCollection = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.CartPaymentCollection = {
    serviceName: utils_1.LINKS.CartPaymentCollection,
    isLink: true,
    databaseConfig: {
        tableName: "cart_payment_collection",
        idPrefix: "capaycol",
    },
    alias: [
        {
            name: ["cart_payment_collection", "cart_payment_collections"],
            entity: "LinkCartPaymentCollection",
        },
    ],
    primaryKeys: ["id", "cart_id", "payment_collection_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            primaryKey: "id",
            foreignKey: "cart_id",
            alias: "cart",
            args: {
                methodSuffix: "Carts",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            primaryKey: "id",
            foreignKey: "payment_collection_id",
            alias: "payment_collection",
            args: {
                methodSuffix: "PaymentCollections",
            },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            fieldAlias: {
                payment_collection: "payment_collection_link.payment_collection",
            },
            relationship: {
                serviceName: utils_1.LINKS.CartPaymentCollection,
                primaryKey: "cart_id",
                foreignKey: "id",
                alias: "payment_collection_link",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            fieldAlias: {
                cart: "cart_link.cart",
            },
            relationship: {
                serviceName: utils_1.LINKS.CartPaymentCollection,
                primaryKey: "payment_collection_id",
                foreignKey: "id",
                alias: "cart_link",
            },
        },
    ],
};
//# sourceMappingURL=cart-payment-collection.js.map