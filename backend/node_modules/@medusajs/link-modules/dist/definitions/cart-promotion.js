"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CartPromotion = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.CartPromotion = {
    serviceName: utils_1.LINKS.CartPromotion,
    isLink: true,
    databaseConfig: {
        tableName: "cart_promotion",
        idPrefix: "cartpromo",
    },
    alias: [
        {
            name: ["cart_promotion", "cart_promotions"],
            entity: "LinkCartPromotion",
        },
    ],
    primaryKeys: ["id", "cart_id", "promotion_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            primaryKey: "id",
            foreignKey: "cart_id",
            alias: "cart",
            args: {
                methodSuffix: "Carts",
            },
            hasMany: true,
        },
        {
            serviceName: utils_1.Modules.PROMOTION,
            entity: "Promotion",
            primaryKey: "id",
            foreignKey: "promotion_id",
            alias: "promotions",
            args: {
                methodSuffix: "Promotions",
            },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            fieldAlias: {
                promotions: {
                    path: "cart_link.promotions",
                    isList: true,
                },
            },
            relationship: {
                serviceName: utils_1.LINKS.CartPromotion,
                primaryKey: "cart_id",
                foreignKey: "id",
                alias: "cart_link",
                isList: true,
            },
        },
    ],
};
//# sourceMappingURL=cart-promotion.js.map