"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublishableApiKeySalesChannel = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.PublishableApiKeySalesChannel = {
    serviceName: utils_1.LINKS.PublishableApiKeySalesChannel,
    isLink: true,
    databaseConfig: {
        tableName: "publishable_api_key_sales_channel",
        idPrefix: "pksc",
    },
    alias: [
        {
            name: [
                "publishable_api_key_sales_channel",
                "publishable_api_key_sales_channels",
            ],
        },
    ],
    primaryKeys: ["id", "publishable_key_id", "sales_channel_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.API_KEY,
            entity: "ApiKey",
            primaryKey: "id",
            foreignKey: "publishable_key_id",
            alias: "api_key",
            args: {
                methodSuffix: "ApiKeys",
            },
            hasMany: true,
        },
        {
            serviceName: utils_1.Modules.SALES_CHANNEL,
            entity: "SalesChannel",
            primaryKey: "id",
            foreignKey: "sales_channel_id",
            alias: "sales_channel",
            args: {
                methodSuffix: "SalesChannels",
            },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.API_KEY,
            entity: "ApiKey",
            fieldAlias: {
                sales_channels: {
                    path: "sales_channels_link.sales_channel",
                    isList: true,
                },
            },
            relationship: {
                serviceName: utils_1.LINKS.PublishableApiKeySalesChannel,
                primaryKey: "publishable_key_id",
                foreignKey: "id",
                alias: "sales_channels_link",
                isList: true,
            },
        },
        {
            serviceName: utils_1.Modules.SALES_CHANNEL,
            entity: "SalesChannel",
            fieldAlias: {
                publishable_api_keys: {
                    path: "api_keys_link.api_key",
                    isList: true,
                },
            },
            relationship: {
                serviceName: utils_1.LINKS.PublishableApiKeySalesChannel,
                primaryKey: "sales_channel_id",
                foreignKey: "id",
                alias: "api_keys_link",
                isList: true,
            },
        },
    ],
};
//# sourceMappingURL=publishable-api-key-sales-channel.js.map