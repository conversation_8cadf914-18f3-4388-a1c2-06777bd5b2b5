"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderClaimPaymentCollection = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.OrderClaimPaymentCollection = {
    serviceName: utils_1.LINKS.OrderClaimPaymentCollection,
    isLink: true,
    databaseConfig: {
        tableName: "order_claim_payment_collection",
        idPrefix: "claimpay",
    },
    alias: [
        {
            name: [
                "order_claim_payment_collection",
                "order_claim_payment_collections",
            ],
            entity: "LinkOrderClaimPaymentCollection",
        },
    ],
    primaryKeys: ["id", "claim_id", "payment_collection_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "OrderClaim",
            primaryKey: "id",
            foreignKey: "claim_id",
            alias: "order",
            args: {
                methodSuffix: "OrderClaims",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            primaryKey: "id",
            foreignKey: "payment_collection_id",
            alias: "payment_collection",
            args: {
                methodSuffix: "PaymentCollections",
            },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "OrderClaim",
            fieldAlias: {
                claim_payment_collections: {
                    path: "claim_payment_collections_link.payment_collection",
                    isList: true,
                },
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderClaimPaymentCollection,
                primaryKey: "claim_id",
                foreignKey: "id",
                alias: "claim_payment_collections_link",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            fieldAlias: {
                claim: "order_claim_link.order",
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderClaimPaymentCollection,
                primaryKey: "payment_collection_id",
                foreignKey: "id",
                alias: "order_claim_link",
            },
        },
    ],
};
//# sourceMappingURL=order-claim-payment-collection.js.map