"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderCart = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.OrderCart = {
    serviceName: utils_1.LINKS.OrderCart,
    isLink: true,
    databaseConfig: {
        tableName: "order_cart",
        idPrefix: "ordercart",
    },
    alias: [
        {
            name: ["order_cart", "order_carts"],
            entity: "LinkOrderCart",
        },
    ],
    primaryKeys: ["id", "order_id", "cart_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "Order",
            primaryKey: "id",
            foreignKey: "order_id",
            alias: "order",
            args: {
                methodSuffix: "Orders",
            },
            hasMany: true,
        },
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            primaryKey: "id",
            foreignKey: "cart_id",
            alias: "cart",
            args: {
                methodSuffix: "Carts",
            },
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "Order",
            fieldAlias: {
                cart: "cart_link.cart",
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderCart,
                primaryKey: "order_id",
                foreignKey: "id",
                alias: "cart_link",
            },
        },
        {
            serviceName: utils_1.Modules.CART,
            entity: "Cart",
            fieldAlias: {
                order: "order_link.order",
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderCart,
                primaryKey: "cart_id",
                foreignKey: "id",
                alias: "order_link",
            },
        },
    ],
};
//# sourceMappingURL=order-cart.js.map