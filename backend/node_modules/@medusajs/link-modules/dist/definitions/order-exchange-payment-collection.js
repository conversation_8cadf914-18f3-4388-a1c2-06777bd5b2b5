"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderExchangePaymentCollection = void 0;
const utils_1 = require("@medusajs/framework/utils");
exports.OrderExchangePaymentCollection = {
    serviceName: utils_1.LINKS.OrderExchangePaymentCollection,
    isLink: true,
    databaseConfig: {
        tableName: "order_exchange_payment_collection",
        idPrefix: "excpay",
    },
    alias: [
        {
            name: [
                "order_exchange_payment_collection",
                "order_exchange_payment_collections",
            ],
            entity: "LinkOrderExchangePaymentCollection",
        },
    ],
    primaryKeys: ["id", "exchange_id", "payment_collection_id"],
    relationships: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "OrderExchange",
            primaryKey: "id",
            foreignKey: "exchange_id",
            alias: "order",
            args: {
                methodSuffix: "OrderExchanges",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            primaryKey: "id",
            foreignKey: "payment_collection_id",
            alias: "payment_collection",
            args: {
                methodSuffix: "PaymentCollections",
            },
            hasMany: true,
        },
    ],
    extends: [
        {
            serviceName: utils_1.Modules.ORDER,
            entity: "OrderExchange",
            fieldAlias: {
                exchange_payment_collections: {
                    path: "exchange_payment_collections_link.payment_collection",
                    isList: true,
                },
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderExchangePaymentCollection,
                primaryKey: "exchange_id",
                foreignKey: "id",
                alias: "exchange_payment_collections_link",
            },
        },
        {
            serviceName: utils_1.Modules.PAYMENT,
            entity: "PaymentCollection",
            fieldAlias: {
                exchange: "order_exchange_link.order",
            },
            relationship: {
                serviceName: utils_1.LINKS.OrderExchangePaymentCollection,
                primaryKey: "payment_collection_id",
                foreignKey: "id",
                alias: "order_exchange_link",
            },
        },
    ],
};
//# sourceMappingURL=order-exchange-payment-collection.js.map