"use strict";
var __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {
    if (kind === "m") throw new TypeError("Private method is not writable");
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a setter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot write private member to an object whose class did not declare it");
    return (kind === "a" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;
};
var __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var _MigrationsExecutionPlanner_dbConfig, _MigrationsExecutionPlanner_unsafeSQLCommands, _MigrationsExecutionPlanner_linksEntities;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MigrationsExecutionPlanner = void 0;
const utils_1 = require("@medusajs/framework/utils");
const postgresql_1 = require("@mikro-orm/postgresql");
const utils_2 = require("../utils");
/**
 * The migrations execution planner creates a plan of SQL queries
 * to be executed to keep link modules database state in sync
 * with the links defined inside the user application.
 */
class MigrationsExecutionPlanner {
    constructor(joinerConfig, options) {
        /**
         * Database options for the module service
         */
        _MigrationsExecutionPlanner_dbConfig.set(this, void 0);
        /**
         * The set of commands that are unsafe to execute automatically when
         * performing "alter table"
         */
        _MigrationsExecutionPlanner_unsafeSQLCommands.set(this, ["alter column", "drop column"]
        /**
         * On-the-fly computed set of entities for the user provided joinerConfig and the link it is coming from
         */
        );
        /**
         * On-the-fly computed set of entities for the user provided joinerConfig and the link it is coming from
         */
        _MigrationsExecutionPlanner_linksEntities.set(this, void 0);
        /**
         * The table that keeps a track of tables generated by the link
         * module.
         */
        this.tableName = "link_module_migrations";
        __classPrivateFieldSet(this, _MigrationsExecutionPlanner_dbConfig, utils_1.ModulesSdkUtils.loadDatabaseConfig("link_modules", options), "f");
        __classPrivateFieldSet(this, _MigrationsExecutionPlanner_linksEntities, joinerConfig
            .map((config) => {
            if (config.isReadOnlyLink) {
                return;
            }
            const [primary, foreign] = config.relationships ?? [];
            const linkDescriptor = {
                fromModule: primary.serviceName,
                toModule: foreign.serviceName,
                fromModel: primary.alias,
                toModel: foreign.alias,
            };
            return {
                entity: (0, utils_2.generateEntity)(config, primary, foreign),
                linkDescriptor,
            };
        })
            .filter((item) => !!item), "f");
    }
    /**
     * Initializes the ORM using the normalized dbConfig and set
     * of provided entities
     */
    async createORM(entities = []) {
        return await utils_1.DALUtils.mikroOrmCreateConnection(__classPrivateFieldGet(this, _MigrationsExecutionPlanner_dbConfig, "f"), entities, "");
    }
    /**
     * Ensure the table to track link modules migrations
     * exists.
     *
     * @param orm MikroORM
     */
    async ensureMigrationsTable(orm) {
        await orm.em.getDriver().getConnection().execute(`
      CREATE TABLE IF NOT EXISTS "${this.tableName}" (
        id SERIAL PRIMARY KEY,
        table_name VARCHAR(255) NOT NULL UNIQUE,
        link_descriptor JSONB NOT NULL DEFAULT '{}'::jsonb,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    }
    /**
     * Ensure the migrations table is in sync
     *
     * @param orm
     * @protected
     */
    async ensureMigrationsTableUpToDate(orm) {
        const existingTables = (await orm.em
            .getDriver()
            .getConnection()
            .execute(`
        SELECT table_name
        FROM information_schema.tables;
    `))
            .map(({ table_name }) => table_name)
            .filter((tableName) => __classPrivateFieldGet(this, _MigrationsExecutionPlanner_linksEntities, "f").some(({ entity }) => entity.meta.collection === tableName));
        if (!existingTables.length) {
            return;
        }
        const orderedDescriptors = existingTables.map((tableName) => {
            return __classPrivateFieldGet(this, _MigrationsExecutionPlanner_linksEntities, "f").find(({ entity }) => entity.meta.collection === tableName).linkDescriptor;
        });
        const positionalArgs = new Array(existingTables.length)
            .fill("(?, ?)")
            .join(", ");
        await orm.em
            .getDriver()
            .getConnection()
            .execute(`
                  INSERT INTO ${this.tableName} (table_name, link_descriptor) VALUES ${positionalArgs} ON CONFLICT DO NOTHING;
        `, existingTables.flatMap((tableName, index) => [
            tableName,
            JSON.stringify(orderedDescriptors[index]),
        ]));
    }
    /**
     * Insert tuple to the migrations table and create the link table
     *
     * @param orm
     * @param action
     * @protected
     */
    async createLinkTable(orm, action) {
        const { tableName, linkDescriptor, sql } = action;
        await orm.em
            .getDriver()
            .getConnection()
            .execute(`
      INSERT INTO "${this.tableName}" (table_name, link_descriptor) VALUES (?, ?);
      ${sql}
    `, [tableName, linkDescriptor]);
    }
    /**
     * Drops the link table and untracks it from the "link_modules_migrations"
     * table.
     */
    async dropLinkTable(orm, tableName) {
        await orm.em.getDriver().getConnection().execute(`
      DROP TABLE IF EXISTS "${tableName}";
      DELETE FROM "${this.tableName}" WHERE table_name = '${tableName}';
    `);
    }
    /**
     * Returns an array of table names that have been tracked during
     * the last run. In short, these tables were created by the
     * link modules migrations runner.
     *
     * @param orm MikroORM
     */
    async getTrackedLinksTables(orm) {
        const results = await orm.em.getDriver().getConnection().execute(`
      SELECT table_name, link_descriptor from "${this.tableName}"
    `);
        return results.map((tuple) => ({
            table_name: tuple.table_name,
            link_descriptor: tuple.link_descriptor,
        }));
    }
    pickTableRelatedCommands(tableName, sqlCommand) {
        const ignoreColumns = ["created_at", "updated_at", "deleted_at"];
        const commands = sqlCommand.split(";");
        const returnedCommands = commands
            .filter((command) => {
            const cmd = command.trim();
            return (cmd.length &&
                cmd !== "set names 'utf8'" &&
                cmd.includes(`"${tableName}"`) &&
                !ignoreColumns.some((column) => cmd.includes(`column "${column}"`)));
        })
            .map((cmd) => cmd.trim());
        if (returnedCommands.length > 0) {
            // adds ; at the end of each command
            returnedCommands.push("");
        }
        return returnedCommands.join(";");
    }
    /**
     * Returns the migration plan for a specific link entity.
     */
    async getEntityMigrationPlan(linkDescriptor, entity, trackedLinksTables) {
        const tableName = entity.meta.collection;
        const orm = await this.createORM([entity]);
        try {
            const generator = orm.getSchemaGenerator();
            const platform = orm.em.getPlatform();
            const connection = orm.em.getConnection();
            const schemaName = __classPrivateFieldGet(this, _MigrationsExecutionPlanner_dbConfig, "f").schema || "public";
            /**
             * If the table name for the entity has not been
             * managed by us earlier, then we should create
             * it.
             */
            if (!trackedLinksTables.includes(tableName)) {
                return {
                    action: "create",
                    linkDescriptor,
                    tableName,
                    sql: (0, utils_1.normalizeMigrationSQL)(await generator.getCreateSchemaSQL()),
                };
            }
            /**
             * Pre-fetching information schema from the database and using that
             * as the way to compute the update diff.
             *
             * @note
             * The "loadInformationSchema" mutates the "dbSchema" argument provided
             * to it as the first argument.
             */
            const dbSchema = new postgresql_1.DatabaseSchema(platform, schemaName);
            await platform
                .getSchemaHelper?.()
                ?.loadInformationSchema(dbSchema, connection, [
                {
                    table_name: tableName,
                    schema_name: schemaName,
                },
            ]);
            let updateSQL = (0, utils_1.normalizeMigrationSQL)(await generator.getUpdateSchemaSQL({
                fromSchema: dbSchema,
            }));
            updateSQL = this.pickTableRelatedCommands(tableName, updateSQL);
            /**
             * Entity is upto-date and hence we do not have to perform
             * any updates on it.
             */
            if (!updateSQL.length) {
                return {
                    action: "noop",
                    linkDescriptor,
                    tableName,
                };
            }
            const usesUnsafeCommands = __classPrivateFieldGet(this, _MigrationsExecutionPlanner_unsafeSQLCommands, "f").some((fragment) => {
                return updateSQL.match(new RegExp(`${fragment}`, "ig"));
            });
            return {
                action: usesUnsafeCommands ? "notify" : "update",
                linkDescriptor,
                tableName,
                sql: updateSQL,
            };
        }
        finally {
            await orm.close(true);
        }
    }
    /**
     * This method loops over the tables we have fetched from the
     * "link_module_migrations" tables and checks if their new
     * name is different from the tracked name and in that
     * case it will rename the actual table and also the
     * tracked entry.
     */
    async migrateOldTables(orm, trackedTables) {
        const migratedTables = [];
        for (let trackedTable of trackedTables) {
            const linkEntity = __classPrivateFieldGet(this, _MigrationsExecutionPlanner_linksEntities, "f").find((entity) => {
                return (entity.linkDescriptor.fromModel ===
                    trackedTable.link_descriptor.fromModel &&
                    entity.linkDescriptor.toModel ===
                        trackedTable.link_descriptor.toModel &&
                    entity.linkDescriptor.fromModule.toLowerCase() ===
                        trackedTable.link_descriptor.fromModule.toLowerCase() &&
                    entity.linkDescriptor.toModule.toLowerCase() ===
                        trackedTable.link_descriptor.toModule.toLowerCase());
            });
            const newTableName = linkEntity?.entity.meta.collection;
            /**
             * Perform rename
             */
            if (newTableName && trackedTable.table_name !== newTableName) {
                await this.renameOldTable(orm, trackedTable.table_name, newTableName, linkEntity.linkDescriptor);
                migratedTables.push({
                    ...trackedTable,
                    table_name: newTableName,
                });
            }
            else {
                migratedTables.push({
                    ...trackedTable,
                });
            }
        }
        return migratedTables;
    }
    /**
     * Renames existing table and also its tracked entry
     */
    async renameOldTable(orm, oldName, newName, descriptor) {
        await orm.em.getDriver().getConnection().execute(`
      ALTER TABLE "${oldName}" RENAME TO "${newName}";
      UPDATE "${this.tableName}" SET table_name = '${newName}', link_descriptor = '${JSON.stringify(descriptor)}' WHERE table_name = '${oldName}';
    `);
    }
    /**
     * Creates a plan to executed in order to keep the database state in
     * sync with the user-defined links.
     *
     * This method only creates a plan and does not change the database
     * state. You must call the "executePlan" method for that.
     */
    async createPlan() {
        const orm = await this.createORM();
        try {
            await this.ensureMigrationsTable(orm);
            const executionActions = [];
            await this.ensureMigrationsTableUpToDate(orm);
            const trackedTables = await this.migrateOldTables(orm, await this.getTrackedLinksTables(orm));
            const trackedTablesNames = trackedTables.map(({ table_name }) => table_name);
            /**
             * Looping through the new set of entities and generating
             * execution plan for them
             */
            for (let { entity, linkDescriptor } of __classPrivateFieldGet(this, _MigrationsExecutionPlanner_linksEntities, "f")) {
                executionActions.push(await this.getEntityMigrationPlan(linkDescriptor, entity, trackedTablesNames));
            }
            const linksTableNames = __classPrivateFieldGet(this, _MigrationsExecutionPlanner_linksEntities, "f").map(({ entity }) => entity.meta.collection);
            /**
             * Finding the tables to be removed
             */
            const tablesToRemove = (0, utils_1.arrayDifference)(trackedTablesNames, linksTableNames);
            tablesToRemove.forEach((tableToRemove) => {
                executionActions.push({
                    action: "delete",
                    tableName: tableToRemove,
                    linkDescriptor: trackedTables.find(({ table_name }) => tableToRemove === table_name).link_descriptor,
                });
            });
            return executionActions;
        }
        finally {
            await orm.close(true);
        }
    }
    /**
     * Executes the actionsPlan actions where the action is one of 'create' | 'update' | 'delete'.
     * 'noop' and 'notify' actions are implicitly ignored. If a notify action needs to be
     * executed, you can mutate its action to 'update', in that scenario it means that an unsafe
     * update sql (from our point of view) will be executed and some data could be lost.
     *
     * @param actionPlan
     */
    async executePlan(actionPlan) {
        const orm = await this.createORM();
        await (0, utils_1.promiseAll)(actionPlan.map(async (action) => {
            switch (action.action) {
                case "delete":
                    return await this.dropLinkTable(orm, action.tableName);
                case "create":
                    return await this.createLinkTable(orm, action);
                case "update":
                    return await orm.em.getDriver().getConnection().execute(action.sql);
                default:
                    return;
            }
        })).finally(() => orm.close(true));
    }
}
exports.MigrationsExecutionPlanner = MigrationsExecutionPlanner;
_MigrationsExecutionPlanner_dbConfig = new WeakMap(), _MigrationsExecutionPlanner_unsafeSQLCommands = new WeakMap(), _MigrationsExecutionPlanner_linksEntities = new WeakMap();
//# sourceMappingURL=index.js.map