import { ILinkMigrationsPlanner, LinkMigrationsPlannerAction, ModuleJoinerConfig, ModuleServiceInitializeOptions, PlannerActionLinkDescriptor } from "@medusajs/framework/types";
import { EntitySchema, MikroORM } from "@mikro-orm/core";
import { PostgreSqlDriver } from "@mikro-orm/postgresql";
/**
 * The migrations execution planner creates a plan of SQL queries
 * to be executed to keep link modules database state in sync
 * with the links defined inside the user application.
 */
export declare class MigrationsExecutionPlanner implements ILinkMigrationsPlanner {
    #private;
    /**
     * The table that keeps a track of tables generated by the link
     * module.
     */
    protected tableName: string;
    constructor(joinerConfig: ModuleJoinerConfig[], options?: ModuleServiceInitializeOptions);
    /**
     * Initializes the ORM using the normalized dbConfig and set
     * of provided entities
     */
    protected createORM(entities?: EntitySchema[]): Promise<MikroORM<PostgreSqlDriver, import("@mikro-orm/postgresql").EntityManager<PostgreSqlDriver> & import("@mikro-orm/core").EntityManager<import("@mikro-orm/core").IDatabaseDriver<import("@mikro-orm/core").Connection>>>>;
    /**
     * Ensure the table to track link modules migrations
     * exists.
     *
     * @param orm MikroORM
     */
    protected ensureMigrationsTable(orm: MikroORM<PostgreSqlDriver>): Promise<void>;
    /**
     * Ensure the migrations table is in sync
     *
     * @param orm
     * @protected
     */
    protected ensureMigrationsTableUpToDate(orm: MikroORM<PostgreSqlDriver>): Promise<void>;
    /**
     * Insert tuple to the migrations table and create the link table
     *
     * @param orm
     * @param action
     * @protected
     */
    protected createLinkTable(orm: MikroORM<PostgreSqlDriver>, action: LinkMigrationsPlannerAction & {
        linkDescriptor: PlannerActionLinkDescriptor;
        sql: string;
    }): Promise<void>;
    /**
     * Drops the link table and untracks it from the "link_modules_migrations"
     * table.
     */
    protected dropLinkTable(orm: MikroORM<PostgreSqlDriver>, tableName: string): Promise<void>;
    /**
     * Returns an array of table names that have been tracked during
     * the last run. In short, these tables were created by the
     * link modules migrations runner.
     *
     * @param orm MikroORM
     */
    protected getTrackedLinksTables(orm: MikroORM<PostgreSqlDriver>): Promise<{
        table_name: string;
        link_descriptor: PlannerActionLinkDescriptor;
    }[]>;
    private pickTableRelatedCommands;
    /**
     * Returns the migration plan for a specific link entity.
     */
    protected getEntityMigrationPlan(linkDescriptor: PlannerActionLinkDescriptor, entity: EntitySchema, trackedLinksTables: string[]): Promise<LinkMigrationsPlannerAction>;
    /**
     * This method loops over the tables we have fetched from the
     * "link_module_migrations" tables and checks if their new
     * name is different from the tracked name and in that
     * case it will rename the actual table and also the
     * tracked entry.
     */
    protected migrateOldTables(orm: MikroORM<PostgreSqlDriver>, trackedTables: {
        table_name: string;
        link_descriptor: PlannerActionLinkDescriptor;
    }[]): Promise<{
        table_name: string;
        link_descriptor: PlannerActionLinkDescriptor;
    }[]>;
    /**
     * Renames existing table and also its tracked entry
     */
    protected renameOldTable(orm: MikroORM<PostgreSqlDriver>, oldName: string, newName: string, descriptor: PlannerActionLinkDescriptor): Promise<void>;
    /**
     * Creates a plan to executed in order to keep the database state in
     * sync with the user-defined links.
     *
     * This method only creates a plan and does not change the database
     * state. You must call the "executePlan" method for that.
     */
    createPlan(): Promise<LinkMigrationsPlannerAction[]>;
    /**
     * Executes the actionsPlan actions where the action is one of 'create' | 'update' | 'delete'.
     * 'noop' and 'notify' actions are implicitly ignored. If a notify action needs to be
     * executed, you can mutate its action to 'update', in that scenario it means that an unsafe
     * update sql (from our point of view) will be executed and some data could be lost.
     *
     * @param actionPlan
     */
    executePlan(actionPlan: LinkMigrationsPlannerAction[]): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map