{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/migration/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAQA,qDAMkC;AAElC,sDAAwE;AACxE,oCAAyC;AAEzC;;;;GAIG;AACH,MAAa,0BAA0B;IA0BrC,YACE,YAAkC,EAClC,OAAwC;QA3B1C;;WAEG;QACH,uDAAgE;QAEhE;;;WAGG;QACH,wDAAqB,CAAC,cAAc,EAAE,aAAa,CAAC;QAEpD;;WAEG;UAJiD;QAEpD;;WAEG;QACH,4DAGG;QAEH;;;WAGG;QACO,cAAS,GAAG,wBAAwB,CAAA;QAM5C,uBAAA,IAAI,wCAAa,uBAAe,CAAC,kBAAkB,CAAC,cAAc,EAAE,OAAO,CAAC,MAAA,CAAA;QAC5E,uBAAA,IAAI,6CAAkB,YAAY;aAC/B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACd,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,OAAM;YACR,CAAC;YAED,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,aAAa,IAAI,EAAE,CAAA;YACrD,MAAM,cAAc,GAAgC;gBAClD,UAAU,EAAE,OAAO,CAAC,WAAW;gBAC/B,QAAQ,EAAE,OAAO,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,KAAK;gBACxB,OAAO,EAAE,OAAO,CAAC,KAAK;aACvB,CAAA;YAED,OAAO;gBACL,MAAM,EAAE,IAAA,sBAAc,EAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;gBAChD,cAAc;aACf,CAAA;QACH,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAA,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,SAAS,CAAC,WAA2B,EAAE;QACrD,OAAO,MAAM,gBAAQ,CAAC,wBAAwB,CAAC,uBAAA,IAAI,4CAAU,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAA;IAC9E,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,qBAAqB,CACnC,GAA+B;QAE/B,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC;oCACjB,IAAI,CAAC,SAAS;;;;;;KAM7C,CAAC,CAAA;IACJ,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,6BAA6B,CAC3C,GAA+B;QAE/B,MAAM,cAAc,GAAa,CAC/B,MAAM,GAAG,CAAC,EAAE;aACT,SAAS,EAAE;aACX,aAAa,EAAE;aACf,OAAO,CAKN;;;KAGL,CACI,CACJ;aACE,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC;aACnC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CACpB,uBAAA,IAAI,iDAAe,CAAC,IAAI,CACtB,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,SAAS,CACrD,CACF,CAAA;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAM;QACR,CAAC;QAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;YAC1D,OAAO,uBAAA,IAAI,iDAAe,CAAC,IAAI,CAC7B,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,KAAK,SAAS,CACpD,CAAC,cAAc,CAAA;QACnB,CAAC,CAAC,CAAA;QAEF,MAAM,cAAc,GAAG,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;aACpD,IAAI,CAAC,QAAQ,CAAC;aACd,IAAI,CAAC,IAAI,CAAC,CAAA;QAEb,MAAM,GAAG,CAAC,EAAE;aACT,SAAS,EAAE;aACX,aAAa,EAAE;aACf,OAAO,CACN;gCACwB,IAAI,CAAC,SAAS,yCAAyC,cAAc;SAC5F,EACD,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,CAAC;YAC3C,SAAS;YACT,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAC1C,CAAC,CACH,CAAA;IACL,CAAC;IAED;;;;;;OAMG;IACO,KAAK,CAAC,eAAe,CAC7B,GAA+B,EAC/B,MAGC;QAED,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,EAAE,GAAG,MAAM,CAAA;QAEjD,MAAM,GAAG,CAAC,EAAE;aACT,SAAS,EAAE;aACX,aAAa,EAAE;aACf,OAAO,CACN;qBACa,IAAI,CAAC,SAAS;QAC3B,GAAG;KACN,EACG,CAAC,SAAS,EAAE,cAAc,CAAC,CAC5B,CAAA;IACL,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,aAAa,CAC3B,GAA+B,EAC/B,SAAiB;QAEjB,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC;8BACvB,SAAS;qBAClB,IAAI,CAAC,SAAS,yBAAyB,SAAS;KAChE,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG;IACO,KAAK,CAAC,qBAAqB,CACnC,GAA+B;QAO/B,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC,OAAO,CAK9D;iDAC2C,IAAI,CAAC,SAAS;KAC1D,CAAC,CAAA;QAEF,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC7B,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,eAAe,EAAE,KAAK,CAAC,eAAe;SACvC,CAAC,CAAC,CAAA;IACL,CAAC;IAEO,wBAAwB,CAAC,SAAiB,EAAE,UAAkB;QACpE,MAAM,aAAa,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;QAChE,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACtC,MAAM,gBAAgB,GAAG,QAAQ;aAC9B,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YAClB,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,EAAE,CAAA;YAC1B,OAAO,CACL,GAAG,CAAC,MAAM;gBACV,GAAG,KAAK,kBAAkB;gBAC1B,GAAG,CAAC,QAAQ,CAAC,IAAI,SAAS,GAAG,CAAC;gBAC9B,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,MAAM,GAAG,CAAC,CAAC,CACpE,CAAA;QACH,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;QAE3B,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,oCAAoC;YACpC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC3B,CAAC;QAED,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACnC,CAAC;IACD;;OAEG;IACO,KAAK,CAAC,sBAAsB,CACpC,cAA2C,EAC3C,MAAoB,EACpB,kBAA4B;QAE5B,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAA;QACxC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAA;QAE1C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,GAAG,CAAC,kBAAkB,EAAE,CAAA;YAC1C,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,CAAA;YACrC,MAAM,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC,aAAa,EAAE,CAAA;YACzC,MAAM,UAAU,GAAG,uBAAA,IAAI,4CAAU,CAAC,MAAM,IAAI,QAAQ,CAAA;YAEpD;;;;eAIG;YACH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,OAAO;oBACL,MAAM,EAAE,QAAQ;oBAChB,cAAc;oBACd,SAAS;oBACT,GAAG,EAAE,IAAA,6BAAqB,EAAC,MAAM,SAAS,CAAC,kBAAkB,EAAE,CAAC;iBACjE,CAAA;YACH,CAAC;YAED;;;;;;;eAOG;YACH,MAAM,QAAQ,GAAG,IAAI,2BAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;YACzD,MAAM,QAAQ;iBACX,eAAe,EAAE,EAAE;gBACpB,EAAE,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE;gBAC5C;oBACE,UAAU,EAAE,SAAS;oBACrB,WAAW,EAAE,UAAU;iBACxB;aACF,CAAC,CAAA;YAEJ,IAAI,SAAS,GAAG,IAAA,6BAAqB,EACnC,MAAM,SAAS,CAAC,kBAAkB,CAAC;gBACjC,UAAU,EAAE,QAAQ;aACrB,CAAC,CACH,CAAA;YAED,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;YAE/D;;;eAGG;YACH,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;gBACtB,OAAO;oBACL,MAAM,EAAE,MAAM;oBACd,cAAc;oBACd,SAAS;iBACV,CAAA;YACH,CAAC;YAED,MAAM,kBAAkB,GAAG,uBAAA,IAAI,qDAAmB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACnE,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;YACzD,CAAC,CAAC,CAAA;YAEF,OAAO;gBACL,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;gBAChD,cAAc;gBACd,SAAS;gBACT,GAAG,EAAE,SAAS;aACf,CAAA;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACvB,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACO,KAAK,CAAC,gBAAgB,CAC9B,GAA+B,EAC/B,aAGG;QAEH,MAAM,cAAc,GAGd,EAAE,CAAA;QAER,KAAK,IAAI,YAAY,IAAI,aAAa,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,uBAAA,IAAI,iDAAe,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACrD,OAAO,CACL,MAAM,CAAC,cAAc,CAAC,SAAS;oBAC7B,YAAY,CAAC,eAAe,CAAC,SAAS;oBACxC,MAAM,CAAC,cAAc,CAAC,OAAO;wBAC3B,YAAY,CAAC,eAAe,CAAC,OAAO;oBACtC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,EAAE;wBAC5C,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,EAAE;oBACvD,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,EAAE;wBAC1C,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,EAAE,CACtD,CAAA;YACH,CAAC,CAAC,CAAA;YACF,MAAM,YAAY,GAAG,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAA;YAEvD;;eAEG;YACH,IAAI,YAAY,IAAI,YAAY,CAAC,UAAU,KAAK,YAAY,EAAE,CAAC;gBAC7D,MAAM,IAAI,CAAC,cAAc,CACvB,GAAG,EACH,YAAY,CAAC,UAAU,EACvB,YAAY,EACZ,UAAU,CAAC,cAAc,CAC1B,CAAA;gBACD,cAAc,CAAC,IAAI,CAAC;oBAClB,GAAG,YAAY;oBACf,UAAU,EAAE,YAAY;iBACzB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,cAAc,CAAC,IAAI,CAAC;oBAClB,GAAG,YAAY;iBAChB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAA;IACvB,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAC5B,GAA+B,EAC/B,OAAe,EACf,OAAe,EACf,UAAuC;QAEvC,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC;qBAChC,OAAO,gBAAgB,OAAO;gBAE3C,IAAI,CAAC,SACP,uBAAuB,OAAO,yBAAyB,IAAI,CAAC,SAAS,CACrE,UAAU,CACX,yBAAyB,OAAO;KAChC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA;QAElC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAA;YAErC,MAAM,gBAAgB,GAAkC,EAAE,CAAA;YAE1D,MAAM,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAA;YAE7C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC/C,GAAG,EACH,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CACtC,CAAA;YACD,MAAM,kBAAkB,GAAG,aAAa,CAAC,GAAG,CAC1C,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,UAAU,CAC/B,CAAA;YAED;;;eAGG;YACH,KAAK,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,uBAAA,IAAI,iDAAe,EAAE,CAAC;gBAC3D,gBAAgB,CAAC,IAAI,CACnB,MAAM,IAAI,CAAC,sBAAsB,CAC/B,cAAc,EACd,MAAM,EACN,kBAAkB,CACnB,CACF,CAAA;YACH,CAAC;YAED,MAAM,eAAe,GAAG,uBAAA,IAAI,iDAAe,CAAC,GAAG,CAC7C,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CACvC,CAAA;YAED;;eAEG;YACH,MAAM,cAAc,GAAG,IAAA,uBAAe,EACpC,kBAAkB,EAClB,eAAe,CAChB,CAAA;YACD,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;gBACvC,gBAAgB,CAAC,IAAI,CAAC;oBACpB,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,aAAa;oBACxB,cAAc,EAAE,aAAa,CAAC,IAAI,CAChC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,aAAa,KAAK,UAAU,CAChD,CAAC,eAAe;iBACnB,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YAEF,OAAO,gBAAgB,CAAA;QACzB,CAAC;gBAAS,CAAC;YACT,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACvB,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CAAC,UAAyC;QACzD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA;QAElC,MAAM,IAAA,kBAAU,EACd,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAC9B,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;gBACxD,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;gBAChD,KAAK,QAAQ;oBACX,OAAO,MAAM,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBACrE;oBACE,OAAM;YACV,CAAC;QACH,CAAC,CAAC,CACH,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAClC,CAAC;CACF;AAteD,gEAseC"}