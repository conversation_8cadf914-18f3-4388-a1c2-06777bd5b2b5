{"version": 3, "file": "container.js", "sourceRoot": "", "sources": ["../../src/loaders/container.ts"], "names": [], "mappings": ";;AAgBA,0CAmDC;AAnED,iDAAiE;AACjE,yCAAyD;AAQzD,qDAKkC;AAClC,mCAAyC;AACzC,SAAgB,eAAe,CAAC,MAAM,EAAE,YAAgC;IACtE,OAAO,KAAK,EACV,EACE,OAAO,EACP,SAAS,GAIV,EACD,iBAA6C,EAC9B,EAAE;QACjB,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,YAAY,CAAC,aAAc,CAAA;QAEtD,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,cAAc;YAC9C,CAAC,CAAC,YAAY,CAAC,WAAW;gBACxB,IAAA,uBAAe,EACb,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,UAAU,CACnB;YACH,CAAC,CAAC,IAAA,kBAAU,EAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;QAEpD,MAAM,UAAU,GAAG,IAAA,oBAAY,EAC7B,OAAO;YACL,CAAC,YAAY,CAAC,cAAc,EAAE,SAAS;gBACrC,IAAA,wBAAgB,EACd,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,UAAU,CACnB,CAAC,CACP,CAAA;QAED,SAAS,CAAC,QAAQ,CAAC;YACjB,YAAY,EAAE,IAAA,gBAAO,EAAC,YAAY,CAAC;YACnC,UAAU,EAAE,IAAA,gBAAO,EAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClD,UAAU,EAAE,IAAA,gBAAO,EAAC,OAAO,CAAC,UAAU,CAAC;YACvC,WAAW,EAAE,IAAA,gBAAO,EAClB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,IAAI,EAAE,CAAC,CAC5D;YAED,iBAAiB,EAAE,IAAA,gBAAO,EAAC,IAAA,4BAAgB,EAAC,YAAY,CAAC,CAAC,CAAC,SAAS,EAAE;YACtE,WAAW,EAAE,IAAA,gBAAO,EAAC,uBAAW,CAAC,CAAC,SAAS,EAAE;YAE7C,cAAc,EAAE,IAAA,gBAAO,EAAC,8BAAc,CAAC,CAAC,SAAS,EAAE;YACnD,cAAc,EAAE,IAAA,gBAAO,EAAC,IAAA,iCAAiB,EAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE;YAC9D,UAAU,EAAE,IAAA,gBAAO,EAAC,UAAU,CAAC;YAC/B,WAAW,EAAE,IAAA,gBAAO,EAAC,WAAW,CAAC;SAClC,CAAC,CAAA;IACJ,CAAC,CAAA;AACH,CAAC"}