"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryModuleService = exports.InventoryLevelService = void 0;
var inventory_level_1 = require("./inventory-level");
Object.defineProperty(exports, "InventoryLevelService", { enumerable: true, get: function () { return __importDefault(inventory_level_1).default; } });
var inventory_module_1 = require("./inventory-module");
Object.defineProperty(exports, "InventoryModuleService", { enumerable: true, get: function () { return __importDefault(inventory_module_1).default; } });
//# sourceMappingURL=index.js.map