import {
  useCountries,
  useCountryTableColumns,
  useCountryTableQuery
} from "./chunk-MB4L63P5.js";
import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import {
  countries
} from "./chunk-HPGXK5DQ.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  arrayType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-XXSIULXV.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-IL7M46GI.js";
import {
  useRegion,
  useUpdateRegion
} from "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  createColumnHelper,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-add-countries-M7O3RG2T.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddCountriesSchema = objectType({
  countries: arrayType(stringType()).min(1)
});
var PAGE_SIZE = 50;
var PREFIX = "ac";
var AddCountriesForm = ({ region }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const form = useForm({
    defaultValues: {
      countries: []
    },
    resolver: t(AddCountriesSchema)
  });
  const { setValue } = form;
  (0, import_react.useEffect)(() => {
    const ids = Object.keys(rowSelection).filter((k) => rowSelection[k]);
    setValue("countries", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
  }, [rowSelection, setValue]);
  const { searchParams, raw } = useCountryTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { countries: countries2, count } = useCountries({
    countries: countries.map((c, i) => ({
      display_name: c.display_name,
      name: c.name,
      id: i,
      iso_2: c.iso_2,
      iso_3: c.iso_3,
      num_code: c.num_code,
      region_id: null,
      region: {}
    })),
    ...searchParams
  });
  const columns = useColumns();
  const { table } = useDataTable({
    data: countries2 || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: (row) => {
      var _a;
      return ((_a = region.countries) == null ? void 0 : _a.findIndex((c) => c.iso_2 === row.original.iso_2)) === -1;
    },
    getRowId: (row) => row.iso_2,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    prefix: PREFIX
  });
  const { mutateAsync, isPending: isLoading } = useUpdateRegion(region.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a;
    const payload = [
      ...((_a = region.countries) == null ? void 0 : _a.map((c) => c.iso_2)) ?? [],
      ...values.countries
    ];
    await mutateAsync(
      {
        countries: payload
      },
      {
        onSuccess: () => {
          toast.success(t2("regions.toast.countries"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", isLoading, type: "submit", children: t2("actions.add") })
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "overflow-hidden", children: (0, import_jsx_runtime.jsx)(
          _DataTable,
          {
            table,
            columns,
            pageSize: PAGE_SIZE,
            count,
            search: "autofocus",
            pagination: true,
            layout: "fill",
            orderBy: [
              { key: "display_name", label: t2("fields.name") },
              { key: "iso_2", label: t2("fields.code") }
            ],
            queryObject: raw,
            prefix: PREFIX
          }
        ) })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCountryTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreselected = !row.getCanSelect();
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected() || isPreselected,
              disabled: isPreselected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base
    ],
    [base]
  );
};
var RegionAddCountries = () => {
  const { id } = useParams();
  const {
    region,
    isPending: isLoading,
    isError,
    error
  } = useRegion(id, {
    fields: "*payment_providers"
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: !isLoading && region && (0, import_jsx_runtime2.jsx)(AddCountriesForm, { region }) });
};
export {
  RegionAddCountries as Component
};
//# sourceMappingURL=region-add-countries-M7O3RG2T-HA4RGC5I.js.map
