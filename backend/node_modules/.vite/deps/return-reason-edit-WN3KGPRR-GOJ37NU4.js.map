{"version": 3, "sources": ["../../@medusajs/dashboard/dist/return-reason-edit-WN3KGPRR.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  useReturnReason,\n  useUpdateReturnReason\n} from \"./chunk-2VTICXJR.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/return-reasons/return-reason-edit/return-reason-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/return-reasons/return-reason-edit/components/return-reason-edit-form/return-reason-edit-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Textarea, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ReturnReasonEditSchema = z.object({\n  value: z.string().min(1),\n  label: z.string().min(1),\n  description: z.string().optional()\n});\nvar ReturnReasonEditForm = ({\n  returnReason\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      value: returnReason.value,\n      label: returnReason.label,\n      description: returnReason.description ?? void 0\n    },\n    resolver: zodResolver(ReturnReasonEditSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateReturnReason(returnReason.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    await mutateAsync(data, {\n      onSuccess: ({ return_reason }) => {\n        toast.success(\n          t(\"returnReasons.edit.successToast\", {\n            label: return_reason.label\n          })\n        );\n        handleSuccess();\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex size-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-4 overflow-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"value\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { tooltip: t(\"returnReasons.fields.value.tooltip\"), children: t(\"returnReasons.fields.value.label\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Input,\n                    {\n                      ...field,\n                      placeholder: t(\"returnReasons.fields.value.placeholder\")\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"label\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"returnReasons.fields.label.label\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Input,\n                    {\n                      ...field,\n                      placeholder: t(\"returnReasons.fields.label.placeholder\")\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"description\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"returnReasons.fields.description.label\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                    Textarea,\n                    {\n                      ...field,\n                      placeholder: t(\n                        \"returnReasons.fields.description.placeholder\"\n                      )\n                    }\n                  ) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", type: \"button\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/return-reasons/return-reason-edit/return-reason-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar ReturnReasonEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const { return_reason, isPending, isError, error } = useReturnReason(id);\n  const ready = !isPending && !!return_reason;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"returnReasons.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"returnReasons.edit.subtitle\") })\n    ] }),\n    ready && /* @__PURE__ */ jsx2(ReturnReasonEditForm, { returnReason: return_reason })\n  ] });\n};\nexport {\n  ReturnReasonEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,yBAA0B;AAmH1B,IAAAA,sBAA2C;AAlH3C,IAAI,yBAAyB,EAAE,OAAO;AAAA,EACpC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACvB,aAAa,EAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AACD,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,OAAO,aAAa;AAAA,MACpB,OAAO,aAAa;AAAA,MACpB,aAAa,aAAa,eAAe;AAAA,IAC3C;AAAA,IACA,UAAU,EAAY,sBAAsB;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,sBAAsB,aAAa,EAAE;AACxE,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AACrD,UAAM,YAAY,MAAM;AAAA,MACtB,WAAW,CAAC,EAAE,cAAc,MAAM;AAChC,cAAM;AAAA,UACJA,GAAE,mCAAmC;AAAA,YACnC,OAAO,cAAc;AAAA,UACvB,CAAC;AAAA,QACH;AACA,sBAAc;AAAA,MAChB;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,8CAA8C,UAAU;AAAA,cAC1F;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,SAASA,GAAE,oCAAoC,GAAG,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,sBACrH,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA,GAAE,wCAAwC;AAAA,oBACzD;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,sBACnE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA,GAAE,wCAAwC;AAAA,oBACzD;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,wCAAwC,EAAE,CAAC;AAAA,sBACzF,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,oBAC5D;AAAA,oBACA;AAAA,sBACE,GAAG;AAAA,sBACH,aAAaA;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,MAAM,UAAU,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvK,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,eAAe,WAAW,SAAS,MAAM,IAAI,gBAAgB,EAAE;AACvE,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,2BAA2B,EAAE,CAAC,EAAE,CAAC;AAAA,UAChI,oBAAAE,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUF,GAAE,6BAA6B,EAAE,CAAC;AAAA,IACpH,EAAE,CAAC;AAAA,IACH,aAAyB,oBAAAE,KAAK,sBAAsB,EAAE,cAAc,cAAc,CAAC;AAAA,EACrF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}