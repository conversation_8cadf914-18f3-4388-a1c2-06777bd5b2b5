import "./chunk-EGRHWZRV.js";
import {
  SectionRow
} from "./chunk-MGJVSCJZ.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import {
  shippingProfileQueryKeys,
  useDeleteShippingProfile,
  useShippingProfile
} from "./chunk-R73OU4H7.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  Trash,
  toast,
  usePrompt
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/shipping-profile-detail-FDUPDSH4.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ShippingProfileDetailBreadcrumb = (props) => {
  const { shipping_profile_id } = props.params || {};
  const { shipping_profile } = useShippingProfile(
    shipping_profile_id,
    void 0,
    {
      initialData: props.data,
      enabled: Boolean(shipping_profile_id)
    }
  );
  if (!shipping_profile) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: shipping_profile.name });
};
var shippingProfileQuery = (id) => ({
  queryKey: shippingProfileQueryKeys.detail(id),
  queryFn: async () => sdk.admin.shippingProfile.retrieve(id)
});
var shippingProfileLoader = async ({ params }) => {
  const id = params.shipping_profile_id;
  const query = shippingProfileQuery(id);
  return queryClient.ensureQueryData(query);
};
var ShippingProfileGeneralSection = ({
  profile
}) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const { mutateAsync } = useDeleteShippingProfile(profile.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("shippingProfile.delete.title"),
      description: t("shippingProfile.delete.description", {
        name: profile.name
      }),
      verificationText: profile.name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(
          t("shippingProfile.delete.successToast", {
            name: profile.name
          })
        );
        navigate("/settings/locations/shipping-profiles", { replace: true });
      },
      onError: (error) => {
        toast.error(error.message);
      }
    });
  };
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: profile.name }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                  label: t("actions.delete"),
                  onClick: handleDelete
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(SectionRow, { title: t("fields.type"), value: profile.type })
  ] });
};
var ShippingProfileDetail = () => {
  const { shipping_profile_id } = useParams();
  const initialData = useLoaderData();
  const { shipping_profile, isLoading, isError, error } = useShippingProfile(
    shipping_profile_id,
    void 0,
    { initialData }
  );
  const { getWidgets } = useExtension();
  if (isLoading || !shipping_profile) {
    return (0, import_jsx_runtime3.jsx)(SingleColumnPageSkeleton, { sections: 1, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("shipping_profile.details.before"),
        after: getWidgets("shipping_profile.details.after")
      },
      showMetadata: true,
      showJSON: true,
      data: shipping_profile,
      children: (0, import_jsx_runtime3.jsx)(ShippingProfileGeneralSection, { profile: shipping_profile })
    }
  );
};
export {
  ShippingProfileDetailBreadcrumb as Breadcrumb,
  ShippingProfileDetail as Component,
  shippingProfileLoader as loader
};
//# sourceMappingURL=shipping-profile-detail-FDUPDSH4-H6MWZ22U.js.map
