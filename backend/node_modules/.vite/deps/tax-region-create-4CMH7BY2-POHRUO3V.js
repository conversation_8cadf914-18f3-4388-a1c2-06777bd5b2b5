import {
  PercentageInput
} from "./chunk-W4P4VL57.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  CountrySelect
} from "./chunk-ZYZ4UOUY.js";
import {
  useComboboxData
} from "./chunk-M3SHND6T.js";
import {
  Combobox
} from "./chunk-CME5W7KH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import "./chunk-YHTHQMGS.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import {
  useCreateTaxRegion
} from "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  InformationCircleSolid,
  Input,
  Text,
  Tooltip,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-create-4CMH7BY2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var TaxRegionCreateSchema = z.object({
  name: z.string().optional(),
  code: z.string().optional(),
  rate: z.object({
    float: z.number().optional(),
    value: z.string().optional()
  }),
  country_code: z.string(),
  provider_id: z.string()
}).superRefine(({ provider_id, country_code }, ctx) => {
  if (!provider_id) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: instance.t("taxRegions.create.errors.missingProvider"),
      path: ["provider_id"]
    });
  }
  if (!country_code) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: instance.t("taxRegions.create.errors.missingCountry"),
      path: ["country_code"]
    });
  }
});
var TaxRegionCreateForm = ({ parentId }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const taxProviders = useComboboxData({
    queryKey: ["tax_providers"],
    queryFn: (params) => sdk.admin.taxProvider.list(params),
    getOptions: (data) => data.tax_providers.map((provider) => ({
      label: formatProvider(provider.id),
      value: provider.id
    }))
  });
  const form = useForm({
    defaultValues: {
      name: "",
      rate: {
        value: ""
      },
      code: "",
      country_code: "",
      provider_id: ""
    },
    resolver: t(TaxRegionCreateSchema)
  });
  const { mutateAsync, isPending } = useCreateTaxRegion();
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a;
    const defaultRate = values.name ? {
      name: values.name,
      rate: ((_a = values.rate) == null ? void 0 : _a.value) === "" ? void 0 : parseFloat(values.rate.value),
      code: values.code
    } : void 0;
    await mutateAsync(
      {
        country_code: values.country_code,
        parent_id: parentId,
        default_tax_rate: defaultRate,
        provider_id: values.provider_id
      },
      {
        onSuccess: ({ tax_region }) => {
          toast.success(t2("taxRegions.create.successToast"));
          handleSuccess(`../${tax_region.id}`);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("taxRegions.create.header") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("taxRegions.create.hint") })
          ] }),
          (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "country_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.country") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "provider_id",
                render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxProvider") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Combobox,
                    {
                      ...field,
                      options: taxProviders.options,
                      searchValue: taxProviders.searchValue,
                      onSearchValueChange: taxProviders.onSearchValueChange,
                      fetchNextPage: taxProviders.fetchNextPage
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] })
              }
            )
          ] }) }),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-4", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-1", children: [
              (0, import_jsx_runtime.jsx)(Heading, { level: "h2", className: "!txt-compact-small-plus", children: t2("taxRegions.fields.defaultTaxRate.label") }),
              (0, import_jsx_runtime.jsxs)(
                Text,
                {
                  size: "small",
                  leading: "compact",
                  className: "text-ui-fg-muted",
                  children: [
                    "(",
                    t2("fields.optional"),
                    ")"
                  ]
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Tooltip,
                {
                  content: t2("taxRegions.fields.defaultTaxRate.tooltip"),
                  children: (0, import_jsx_runtime.jsx)(InformationCircleSolid, { className: "text-ui-fg-muted" })
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-1 gap-4 md:grid-cols-2", children: [
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "name",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "rate",
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxRate") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        PercentageInput,
                        {
                          ...field,
                          value: value == null ? void 0 : value.value,
                          onValueChange: (value2, _name, values) => onChange({
                            value: value2,
                            float: values == null ? void 0 : values.float
                          })
                        }
                      ) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "code",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxCode") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] }) })
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxRegionCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(TaxRegionCreateForm, {}) });
};
export {
  TaxRegionCreate as Component,
  TaxRegionCreate
};
//# sourceMappingURL=tax-region-create-4CMH7BY2-POHRUO3V.js.map
