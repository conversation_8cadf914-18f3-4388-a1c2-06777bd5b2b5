{"version": 3, "sources": ["../../@medusajs/dashboard/dist/sales-channel-edit-DQYAVVKY.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useSalesChannel,\n  useUpdateSalesChannel\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/sales-channels/sales-channel-edit/sales-channel-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/sales-channels/sales-channel-edit/components/edit-sales-channel-form/edit-sales-channel-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, Input, Switch, Textarea, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar EditSalesChannelSchema = zod.object({\n  name: zod.string().min(1),\n  description: zod.string().optional(),\n  is_active: zod.boolean()\n});\nvar EditSalesChannelForm = ({\n  salesChannel\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: salesChannel.name,\n      description: salesChannel.description ?? \"\",\n      is_active: !salesChannel.is_disabled\n    },\n    resolver: zodResolver(EditSalesChannelSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateSalesChannel(salesChannel.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        name: values.name,\n        description: values.description ?? void 0,\n        is_disabled: !values.is_active\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"salesChannels.toast.update\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsxs(RouteDrawer.Body, { className: \"flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto\", children: [\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"name\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field, size: \"small\" }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"description\",\n              render: ({ field }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsx(Form.Label, { optional: true, children: t(\"fields.description\") }),\n                  /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          ),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"is_active\",\n              render: ({ field: { onChange, value, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"general.enabled\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Switch,\n                      {\n                        onCheckedChange: onChange,\n                        checked: value,\n                        ...field\n                      }\n                    ) })\n                  ] }),\n                  /* @__PURE__ */ jsx(Form.Hint, { children: t(\"salesChannels.enabledHint\") }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/sales-channels/sales-channel-edit/sales-channel-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar SalesChannelEdit = () => {\n  const { id } = useParams();\n  const { t } = useTranslation2();\n  const {\n    sales_channel,\n    isPending: isLoading,\n    isError,\n    error\n  } = useSalesChannel(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx2(RouteDrawer.Header, { children: /* @__PURE__ */ jsx2(Heading, { className: \"capitalize\", children: t(\"salesChannels.editSalesChannel\") }) }),\n    !isLoading && !!sales_channel && /* @__PURE__ */ jsx2(EditSalesChannelForm, { salesChannel: sales_channel })\n  ] });\n};\nexport {\n  SalesChannelEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,yBAA0B;AA4G1B,IAAAA,sBAA2C;AA3G3C,IAAI,yBAA6B,WAAO;AAAA,EACtC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,aAAiB,WAAO,EAAE,SAAS;AAAA,EACnC,WAAe,YAAQ;AACzB,CAAC;AACD,IAAI,uBAAuB,CAAC;AAAA,EAC1B;AACF,MAAM;AACJ,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM,aAAa;AAAA,MACnB,aAAa,aAAa,eAAe;AAAA,MACzC,WAAW,CAAC,aAAa;AAAA,IAC3B;AAAA,IACA,UAAU,EAAY,sBAAsB;AAAA,EAC9C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,sBAAsB,aAAa,EAAE;AACxE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,QACb,aAAa,OAAO,eAAe;AAAA,QACnC,aAAa,CAAC,OAAO;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,4BAA4B,CAAC;AAC7C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,yBAAK,YAAY,MAAM,EAAE,WAAW,2DAA2D,UAAU;AAAA,cACvG;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,sBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,OAAO,MAAM,QAAQ,CAAC,EAAE,CAAC;AAAA,sBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,wBAAI,KAAK,OAAO,EAAE,UAAU,MAAM,UAAUA,GAAE,oBAAoB,EAAE,CAAC;AAAA,sBACrE,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,sBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,cACgB;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,OAAO,GAAG,MAAM,EAAE,MAAM;AACpD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,wBACtE,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,wBAClD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,iBAAiB;AAAA,wBACjB,SAAS;AAAA,wBACT,GAAG;AAAA,sBACL;AAAA,oBACF,EAAE,CAAC;AAAA,kBACL,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,sBAC3D,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,YAAY,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAC9H,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,mBAAmB,MAAM;AAC3B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,gBAAgB,EAAE;AACtB,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAC,KAAK,YAAY,QAAQ,EAAE,cAA0B,oBAAAA,KAAK,SAAS,EAAE,WAAW,cAAc,UAAUF,GAAE,gCAAgC,EAAE,CAAC,EAAE,CAAC;AAAA,IAChK,CAAC,aAAa,CAAC,CAAC,qBAAiC,oBAAAE,KAAK,sBAAsB,EAAE,cAAc,cAAc,CAAC;AAAA,EAC7G,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}