{"version": 3, "sources": ["../../@medusajs/dashboard/dist/product-type-detail-B5ROLSWV.mjs"], "sourcesContent": ["import {\n  useDeleteProductTypeAction\n} from \"./chunk-S22NYSST.mjs\";\nimport {\n  useProductTableColumns\n} from \"./chunk-G3QXMPRB.mjs\";\nimport {\n  useProductTableQuery\n} from \"./chunk-PCFUZKDS.mjs\";\nimport \"./chunk-IQBAUTU5.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-B2JT2FOA.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  useProductTableFilters\n} from \"./chunk-FZRIVT5D.mjs\";\nimport \"./chunk-GJUPECDU.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport \"./chunk-MNXC6Q4F.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport {\n  productTypesQueryKeys,\n  useProductType\n} from \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport {\n  useProducts\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/product-types/product-type-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ProductTypeDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { product_type } = useProductType(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!product_type) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: product_type.value });\n};\n\n// src/routes/product-types/product-type-detail/loader.ts\nvar productTypeDetailQuery = (id) => ({\n  queryKey: productTypesQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.productType.retrieve(id)\n});\nvar productTypeLoader = async ({ params }) => {\n  const id = params.id;\n  const query = productTypeDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/product-types/product-type-detail/product-type-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/product-types/product-type-detail/components/product-type-general-section/product-type-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, Heading } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar ProductTypeGeneralSection = ({\n  productType\n}) => {\n  const { t } = useTranslation();\n  const handleDelete = useDeleteProductTypeAction(\n    productType.id,\n    productType.value\n  );\n  return /* @__PURE__ */ jsxs(Container, { className: \"flex items-center justify-between\", children: [\n    /* @__PURE__ */ jsx2(Heading, { children: productType.value }),\n    /* @__PURE__ */ jsx2(\n      ActionMenu,\n      {\n        groups: [\n          {\n            actions: [\n              {\n                label: t(\"actions.edit\"),\n                icon: /* @__PURE__ */ jsx2(PencilSquare, {}),\n                to: \"edit\"\n              }\n            ]\n          },\n          {\n            actions: [\n              {\n                label: t(\"actions.delete\"),\n                icon: /* @__PURE__ */ jsx2(Trash, {}),\n                onClick: handleDelete\n              }\n            ]\n          }\n        ]\n      }\n    )\n  ] });\n};\n\n// src/routes/product-types/product-type-detail/components/product-type-product-section/product-type-product-section.tsx\nimport { Container as Container2, Heading as Heading2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 10;\nvar ProductTypeProductSection = ({\n  productType\n}) => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useProductTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { products, count, isPending, isError, error } = useProducts({\n    ...searchParams,\n    type_id: [productType.id]\n  });\n  const filters = useProductTableFilters([\"product_types\"]);\n  const columns = useProductTableColumns();\n  const { table } = useDataTable({\n    columns,\n    data: products,\n    count: products?.length || 0,\n    getRowId: (row) => row.id,\n    pageSize: PAGE_SIZE\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsx3(\"div\", { className: \"px-6 py-4\", children: /* @__PURE__ */ jsx3(Heading2, { level: \"h2\", children: t(\"products.domain\") }) }),\n    /* @__PURE__ */ jsx3(\n      _DataTable,\n      {\n        table,\n        filters,\n        isLoading: isPending,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        navigateTo: ({ original }) => `/products/${original.id}`,\n        orderBy: [\n          { key: \"title\", label: t(\"fields.title\") },\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ],\n        queryObject: raw,\n        search: true,\n        pagination: true\n      }\n    )\n  ] });\n};\n\n// src/routes/product-types/product-type-detail/product-type-detail.tsx\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nvar ProductTypeDetail = () => {\n  const { id } = useParams();\n  const initialData = useLoaderData();\n  const { product_type, isPending, isError, error } = useProductType(\n    id,\n    void 0,\n    {\n      initialData\n    }\n  );\n  const { getWidgets } = useExtension();\n  if (isPending || !product_type) {\n    return /* @__PURE__ */ jsx4(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"product_type.details.after\"),\n        before: getWidgets(\"product_type.details.before\")\n      },\n      showJSON: true,\n      showMetadata: true,\n      data: product_type,\n      children: [\n        /* @__PURE__ */ jsx4(ProductTypeGeneralSection, { productType: product_type }),\n        /* @__PURE__ */ jsx4(ProductTypeProductSection, { productType: product_type })\n      ]\n    }\n  );\n};\nexport {\n  ProductTypeDetailBreadcrumb as Breadcrumb,\n  ProductTypeDetail as Component,\n  productTypeLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA,yBAAoB;AA+BpB,IAAAA,sBAAkC;AA0ClC,IAAAC,sBAA2C;AAmD3C,IAAAA,sBAA2C;AA3H3C,IAAI,8BAA8B,CAAC,UAAU;AAC3C,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,aAAa,IAAI,eAAe,IAAI,QAAQ;AAAA,IAClD,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,aAAa,MAAM,CAAC;AACrE;AAGA,IAAI,yBAAyB,CAAC,QAAQ;AAAA,EACpC,UAAU,sBAAsB,OAAO,EAAE;AAAA,EACzC,SAAS,YAAY,IAAI,MAAM,YAAY,SAAS,EAAE;AACxD;AACA,IAAI,oBAAoB,OAAO,EAAE,OAAO,MAAM;AAC5C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,uBAAuB,EAAE;AACvC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAUA,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,eAAe;AAAA,IACnB,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,qCAAqC,UAAU;AAAA,QACjF,oBAAAC,KAAK,SAAS,EAAE,UAAU,YAAY,MAAM,CAAC;AAAA,QAC7C,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,OAAO,EAAE,cAAc;AAAA,gBACvB,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,IAAI;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,cACP;AAAA,gBACE,OAAO,EAAE,gBAAgB;AAAA,gBACzB,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,gBACpC,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAMA,IAAI,YAAY;AAChB,IAAI,4BAA4B,CAAC;AAAA,EAC/B;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,UAAU,OAAO,WAAW,SAAS,MAAM,IAAI,YAAY;AAAA,IACjE,GAAG;AAAA,IACH,SAAS,CAAC,YAAY,EAAE;AAAA,EAC1B,CAAC;AACD,QAAM,UAAU,uBAAuB,CAAC,eAAe,CAAC;AACxD,QAAM,UAAU,uBAAuB;AACvC,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B;AAAA,IACA,MAAM;AAAA,IACN,QAAO,qCAAU,WAAU;AAAA,IAC3B,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,cAA0B,oBAAAA,KAAK,SAAU,EAAE,OAAO,MAAM,UAAU,EAAE,iBAAiB,EAAE,CAAC,EAAE,CAAC;AAAA,QACjI,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,YAAY,CAAC,EAAE,SAAS,MAAM,aAAa,SAAS,EAAE;AAAA,QACtD,SAAS;AAAA,UACP,EAAE,KAAK,SAAS,OAAO,EAAE,cAAc,EAAE;AAAA,UACzC,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,QACA,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,oBAAoB,MAAM;AAC5B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,cAAc,WAAW,SAAS,MAAM,IAAI;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,MACE;AAAA,IACF;AAAA,EACF;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,cAAc;AAC9B,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,4BAA4B;AAAA,QAC9C,QAAQ,WAAW,6BAA6B;AAAA,MAClD;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd,MAAM;AAAA,MACN,UAAU;AAAA,YACQ,oBAAAD,KAAK,2BAA2B,EAAE,aAAa,aAAa,CAAC;AAAA,YAC7D,oBAAAA,KAAK,2BAA2B,EAAE,aAAa,aAAa,CAAC;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsxs2", "jsx3", "jsx4", "jsxs3"]}