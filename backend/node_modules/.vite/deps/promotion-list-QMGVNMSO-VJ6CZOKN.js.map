{"version": 3, "sources": ["../../@medusajs/dashboard/dist/promotion-list-QMGVNMSO.mjs"], "sourcesContent": ["import {\n  usePromotionTableColumns,\n  usePromotionTableQuery\n} from \"./chunk-VAI6AJFG.mjs\";\nimport \"./chunk-JRIZAFLU.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-B2JT2FOA.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  usePromotionTableFilters\n} from \"./chunk-LSEYENCI.mjs\";\nimport \"./chunk-GJUPECDU.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-GWO5QQQW.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport {\n  promotionsQueryKeys,\n  useDeletePromotion,\n  usePromotions\n} from \"./chunk-G2H6MAK7.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/promotions/promotion-list/loader.ts\nvar params = {\n  limit: 20,\n  offset: 0\n};\nvar promotionsListQuery = () => ({\n  queryKey: promotionsQueryKeys.list(params),\n  queryFn: async () => sdk.admin.promotion.list(params)\n});\nvar promotionsLoader = (client) => {\n  return async () => {\n    const query = promotionsListQuery();\n    return queryClient.getQueryData(\n      query.queryKey\n    ) ?? await client.fetchQuery(query);\n  };\n};\n\n// src/routes/promotions/promotion-list/components/promotion-list-table/promotion-list-table.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Button, Container, Heading, usePrompt } from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { Link, Outlet, useLoaderData, useNavigate } from \"react-router-dom\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar PromotionListTable = () => {\n  const { t } = useTranslation();\n  const initialData = useLoaderData();\n  const { searchParams, raw } = usePromotionTableQuery({ pageSize: PAGE_SIZE });\n  const { promotions, count, isLoading, isError, error } = usePromotions(\n    { ...searchParams },\n    {\n      initialData,\n      placeholderData: keepPreviousData\n    }\n  );\n  const filters = usePromotionTableFilters();\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: promotions ?? [],\n    columns,\n    count,\n    enablePagination: true,\n    pageSize: PAGE_SIZE,\n    getRowId: (row) => row.id\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx(Heading, { level: \"h2\", children: t(\"promotions.domain\") }),\n      /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", asChild: true, children: /* @__PURE__ */ jsx(Link, { to: \"create\", children: t(\"actions.create\") }) })\n    ] }),\n    /* @__PURE__ */ jsx(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        pageSize: PAGE_SIZE,\n        filters,\n        search: true,\n        pagination: true,\n        isLoading,\n        queryObject: raw,\n        navigateTo: (row) => `${row.original.id}`,\n        orderBy: [\n          { key: \"created_at\", label: t(\"fields.createdAt\") },\n          { key: \"updated_at\", label: t(\"fields.updatedAt\") }\n        ]\n      }\n    ),\n    /* @__PURE__ */ jsx(Outlet, {})\n  ] });\n};\nvar PromotionActions = ({ promotion }) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeletePromotion(promotion.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"promotions.deleteWarning\", { code: promotion.code }),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\"),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      verificationText: promotion.code\n    });\n    if (!res) {\n      return;\n    }\n    try {\n      await mutateAsync(void 0, {\n        onSuccess: () => {\n          navigate(\"/promotions\", { replace: true });\n        }\n      });\n    } catch {\n      throw new Error(\n        `Promotion with code ${promotion.code} could not be deleted`\n      );\n    }\n  };\n  return /* @__PURE__ */ jsx(\n    ActionMenu,\n    {\n      groups: [\n        {\n          actions: [\n            {\n              icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n              label: t(\"actions.edit\"),\n              to: `/promotions/${promotion.id}/edit`\n            },\n            {\n              icon: /* @__PURE__ */ jsx(Trash, {}),\n              label: t(\"actions.delete\"),\n              onClick: handleDelete\n            }\n          ]\n        }\n      ]\n    }\n  );\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = usePromotionTableColumns();\n  return useMemo(\n    () => [\n      ...base,\n      columnHelper.display({\n        id: \"actions\",\n        cell: ({ row }) => {\n          return /* @__PURE__ */ jsx(PromotionActions, { promotion: row.original });\n        }\n      })\n    ],\n    [base]\n  );\n};\n\n// src/routes/promotions/promotion-list/promotions-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar PromotionsList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"promotion.list.before\"),\n        after: getWidgets(\"promotion.list.after\")\n      },\n      children: /* @__PURE__ */ jsx2(PromotionListTable, {})\n    }\n  );\n};\nexport {\n  PromotionsList as Component,\n  promotionsLoader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA,mBAAwB;AAIxB,yBAA0B;AA0H1B,IAAAA,sBAA4B;AAnJ5B,IAAI,SAAS;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAI,sBAAsB,OAAO;AAAA,EAC/B,UAAU,oBAAoB,KAAK,MAAM;AAAA,EACzC,SAAS,YAAY,IAAI,MAAM,UAAU,KAAK,MAAM;AACtD;AACA,IAAI,mBAAmB,CAAC,WAAW;AACjC,SAAO,YAAY;AACjB,UAAM,QAAQ,oBAAoB;AAClC,WAAO,YAAY;AAAA,MACjB,MAAM;AAAA,IACR,KAAK,MAAM,OAAO,WAAW,KAAK;AAAA,EACpC;AACF;AAWA,IAAI,YAAY;AAChB,IAAI,qBAAqB,MAAM;AAC7B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,cAAc,IAAI,IAAI,uBAAuB,EAAE,UAAU,UAAU,CAAC;AAC5E,QAAM,EAAE,YAAY,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IACvD,EAAE,GAAG,aAAa;AAAA,IAClB;AAAA,MACE;AAAA,MACA,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,yBAAyB;AACzC,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU,CAAC,QAAQ,IAAI;AAAA,EACzB,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,yBAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,yBAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,wBAAI,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,mBAAmB,EAAE,CAAC;AAAA,UAC9D,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,SAAS,MAAM,cAA0B,wBAAI,MAAM,EAAE,IAAI,UAAU,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,IAC1K,EAAE,CAAC;AAAA,QACa;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ;AAAA,QACA,aAAa;AAAA,QACb,YAAY,CAAC,QAAQ,GAAG,IAAI,SAAS,EAAE;AAAA,QACvC,SAAS;AAAA,UACP,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,UAClD,EAAE,KAAK,cAAc,OAAO,EAAE,kBAAkB,EAAE;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,QACgB,wBAAI,QAAQ,CAAC,CAAC;AAAA,EAChC,EAAE,CAAC;AACL;AACA,IAAI,mBAAmB,CAAC,EAAE,UAAU,MAAM;AACxC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,mBAAmB,UAAU,EAAE;AACvD,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,4BAA4B,EAAE,MAAM,UAAU,KAAK,CAAC;AAAA,MACnE,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,MAC9B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,kBAAkB,UAAU;AAAA,IAC9B,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,QAAI;AACF,YAAM,YAAY,QAAQ;AAAA,QACxB,WAAW,MAAM;AACf,mBAAS,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH,QAAQ;AACN,YAAM,IAAI;AAAA,QACR,uBAAuB,UAAU,IAAI;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AACA,aAAuB;AAAA,IACrB;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,QACN;AAAA,UACE,SAAS;AAAA,YACP;AAAA,cACE,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,cAC1C,OAAO,EAAE,cAAc;AAAA,cACvB,IAAI,eAAe,UAAU,EAAE;AAAA,YACjC;AAAA,YACA;AAAA,cACE,UAAsB,wBAAI,OAAO,CAAC,CAAC;AAAA,cACnC,OAAO,EAAE,gBAAgB;AAAA,cACzB,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,yBAAyB;AACtC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,qBAAuB,wBAAI,kBAAkB,EAAE,WAAW,IAAI,SAAS,CAAC;AAAA,QAC1E;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AAIA,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,uBAAuB;AAAA,QAC1C,OAAO,WAAW,sBAAsB;AAAA,MAC1C;AAAA,MACA,cAA0B,oBAAAA,KAAK,oBAAoB,CAAC,CAAC;AAAA,IACvD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}