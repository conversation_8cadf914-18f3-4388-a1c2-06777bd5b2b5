import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  useComboboxData
} from "./chunk-M3SHND6T.js";
import {
  Combobox
} from "./chunk-CME5W7KH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import {
  useTaxRegion,
  useUpdateTaxRegion
} from "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-edit-GHZ22EEA.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var TaxRegionEditSchema = z.object({
  provider_id: z.string().min(1)
});
var TaxRegionEditForm = ({ taxRegion }) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const taxProviders = useComboboxData({
    queryKey: ["tax_providers"],
    queryFn: (params) => sdk.admin.taxProvider.list(params),
    getOptions: (data) => data.tax_providers.map((provider) => ({
      label: formatProvider(provider.id),
      value: provider.id
    }))
  });
  const form = useForm({
    defaultValues: {
      provider_id: taxRegion.provider_id
    },
    resolver: t(TaxRegionEditSchema)
  });
  const { mutateAsync, isPending } = useUpdateTaxRegion(taxRegion.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        provider_id: values.provider_id
      },
      {
        onSuccess: () => {
          toast.success(t2("taxRegions.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex flex-1 flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-6 overflow-auto", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col gap-y-4", children: (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "provider_id",
            render: ({ field }) => (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
              (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxProvider") }),
              (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                Combobox,
                {
                  ...field,
                  options: taxProviders.options,
                  searchValue: taxProviders.searchValue,
                  onSearchValueChange: taxProviders.onSearchValueChange,
                  fetchNextPage: taxProviders.fetchNextPage
                }
              ) }),
              (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
            ] })
          }
        ) }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { className: "shrink-0", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxRegionEdit = () => {
  const { t: t2 } = useTranslation();
  const { id } = useParams();
  const { tax_region, isPending, isError, error } = useTaxRegion(id);
  const ready = !isPending && !!tax_region;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("taxRegions.edit.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { className: "sr-only", children: t2("taxRegions.edit.hint") })
    ] }),
    ready && (0, import_jsx_runtime2.jsx)(TaxRegionEditForm, { taxRegion: tax_region })
  ] });
};
export {
  TaxRegionEdit as Component
};
//# sourceMappingURL=tax-region-edit-GHZ22EEA-DCOGLPIF.js.map
