import {
  TaxOverrideTable,
  TaxRateLine,
  useTaxOverrideTable
} from "./chunk-5E7YNF73.js";
import "./chunk-VD6KBTYK.js";
import {
  TaxRegionCard
} from "./chunk-HELIVXTB.js";
import {
  getProvinceByIso2,
  isProvinceInCountry
} from "./chunk-KL72CHML.js";
import {
  useTaxRateTableQuery
} from "./chunk-GDXEFZZY.js";
import "./chunk-XYHEMHQ5.js";
import "./chunk-QF476XOZ.js";
import "./chunk-HM7ZXGFD.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-32T72GVU.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-XXSIULXV.js";
import "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import {
  useTaxRates
} from "./chunk-UHLJOZH7.js";
import {
  taxRegionsQueryKeys,
  useTaxRegion
} from "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Container,
  Heading,
  Tooltip
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-province-detail-OOYBGAGO.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var TaxRegionProvinceDetailSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const defaultRates = taxRegion.tax_rates.filter((r) => r.is_default === true);
  const showBage = defaultRates.length === 0;
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsx)(
      TaxRegionCard,
      {
        taxRegion,
        type: "header",
        asLink: false,
        badge: showBage && (0, import_jsx_runtime.jsx)(Tooltip, { content: t("taxRegions.fields.noDefaultRate.tooltip"), children: (0, import_jsx_runtime.jsx)(Badge, { color: "orange", size: "2xsmall", className: "cursor-default", children: t("taxRegions.fields.noDefaultRate.label") }) })
      }
    ),
    defaultRates.map((rate) => {
      return (0, import_jsx_runtime.jsx)(TaxRateLine, { taxRate: rate, isSublevelTaxRate: true }, rate.id);
    })
  ] });
};
var PAGE_SIZE = 10;
var PREFIX = "o";
var TaxRegionProvinceOverrideSection = ({
  taxRegion
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useTaxRateTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { tax_rates, count, isPending, isError, error } = useTaxRates(
    {
      ...searchParams,
      tax_region_id: taxRegion.id,
      is_default: false
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const { table } = useTaxOverrideTable({
    count,
    data: tax_rates,
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime2.jsx)(
    TaxOverrideTable,
    {
      isPending,
      table,
      count,
      action: {
        label: t("actions.create"),
        to: "overrides/create"
      },
      queryObject: raw,
      prefix: PREFIX,
      children: (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("taxRegions.taxOverrides.header") })
    }
  ) });
};
var TaxRegionDetail = () => {
  const { province_id } = useParams();
  const initialData = useLoaderData();
  const {
    tax_region: taxRegion,
    isLoading,
    isError,
    error
  } = useTaxRegion(province_id, void 0, { initialData });
  const { getWidgets } = useExtension();
  if (isLoading || !taxRegion) {
    return (0, import_jsx_runtime3.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    SingleColumnPage,
    {
      data: taxRegion,
      showJSON: true,
      widgets: {
        after: getWidgets("tax.details.after"),
        before: getWidgets("tax.details.before")
      },
      children: [
        (0, import_jsx_runtime3.jsx)(TaxRegionProvinceDetailSection, { taxRegion }),
        (0, import_jsx_runtime3.jsx)(TaxRegionProvinceOverrideSection, { taxRegion })
      ]
    }
  );
};
var TaxRegionDetailBreadcrumb = (props) => {
  var _a, _b;
  const { province_id } = props.params || {};
  const { tax_region } = useTaxRegion(province_id, void 0, {
    initialData: props.data,
    enabled: Boolean(province_id)
  });
  if (!tax_region) {
    return null;
  }
  const countryCode = (_a = tax_region.country_code) == null ? void 0 : _a.toUpperCase();
  const provinceCode = (_b = tax_region.province_code) == null ? void 0 : _b.toUpperCase();
  const isValid = isProvinceInCountry(countryCode, provinceCode);
  return (0, import_jsx_runtime4.jsx)("span", { children: isValid ? getProvinceByIso2(provinceCode) : provinceCode });
};
var taxRegionDetailQuery = (id) => ({
  queryKey: taxRegionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.taxRegion.retrieve(id)
});
var taxRegionLoader = async ({ params }) => {
  const id = params.province_id;
  const query = taxRegionDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
export {
  TaxRegionDetailBreadcrumb as Breadcrumb,
  TaxRegionDetail as Component,
  TaxRegionDetail,
  taxRegionLoader as loader
};
//# sourceMappingURL=tax-region-province-detail-OOYBGAGO-ZNC6GI5I.js.map
