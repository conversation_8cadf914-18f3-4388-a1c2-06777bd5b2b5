import "./chunk-5GF3RGIE.js";
import {
  Outlet,
  useLocation,
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import "./chunk-RPCDYKBN.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/settings-2VXIGDVD.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Settings = () => {
  const navigate = useNavigate();
  const location = useLocation();
  (0, import_react.useEffect)(() => {
    if (location.pathname === "/settings") {
      navigate("/settings/store", { replace: true });
    }
  }, [location.pathname, navigate]);
  return (0, import_jsx_runtime.jsx)(Outlet, {});
};
export {
  Settings as Component
};
//# sourceMappingURL=settings-2VXIGDVD-AXTJPDWW.js.map
