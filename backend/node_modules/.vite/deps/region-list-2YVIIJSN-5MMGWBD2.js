import {
  useRegionTableColumns
} from "./chunk-M4KSNHG3.js";
import "./chunk-2BWFXI2Q.js";
import {
  useRegionTableQuery
} from "./chunk-ASBI7JIX.js";
import "./chunk-LVAKEKGS.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-32T72GVU.js";
import {
  useRegionTableFilters
} from "./chunk-I7242KR3.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import "./chunk-HPGXK5DQ.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useDeleteRegion,
  useRegions
} from "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-list-2YVIIJSN.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var RegionListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useRegionTableQuery({ pageSize: PAGE_SIZE });
  const {
    regions,
    count,
    isPending: isLoading,
    isError,
    error
  } = useRegions(
    {
      ...searchParams,
      fields: "*payment_providers"
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useRegionTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    data: regions ?? [],
    columns,
    count,
    enablePagination: true,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t("regions.domain") }),
        (0, import_jsx_runtime.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("regions.subtitle") })
      ] }),
      (0, import_jsx_runtime.jsx)(Link, { to: "/settings/regions/create", children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        columns,
        count,
        pageSize: PAGE_SIZE,
        isLoading,
        filters,
        orderBy: [
          { key: "name", label: t("fields.name") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        navigateTo: (row) => `${row.original.id}`,
        pagination: true,
        search: true,
        queryObject: raw,
        noRecords: {
          message: t("regions.list.noRecordsMessage")
        }
      }
    )
  ] });
};
var RegionActions = ({ region }) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useDeleteRegion(region.id);
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("regions.deleteRegionWarning", {
        name: region.name
      }),
      verificationText: region.name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("regions.toast.delete"));
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              to: `/settings/regions/${region.id}/edit`,
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {})
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              onClick: handleDelete,
              icon: (0, import_jsx_runtime.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useRegionTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime.jsx)(RegionActions, { region: row.original });
        }
      })
    ],
    [base]
  );
};
var RegionList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("region.list.before"),
        after: getWidgets("region.list.after")
      },
      children: (0, import_jsx_runtime2.jsx)(RegionListTable, {})
    }
  );
};
export {
  RegionList as Component
};
//# sourceMappingURL=region-list-2YVIIJSN-5MMGWBD2.js.map
