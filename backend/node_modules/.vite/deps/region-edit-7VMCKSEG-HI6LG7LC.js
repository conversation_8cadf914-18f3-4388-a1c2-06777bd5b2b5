import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import {
  Combobox
} from "./chunk-CME5W7KH.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import "./chunk-IA4ROPJA.js";
import {
  arrayType,
  booleanType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import {
  usePaymentProviders
} from "./chunk-QGTAAHL2.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import {
  useStore
} from "./chunk-YXXDSYQ5.js";
import {
  useRegion,
  useUpdateRegion
} from "./chunk-NWAMKOL4.js";
import {
  usePricePreferences
} from "./chunk-6TPPQSEA.js";
import "./chunk-SZTMXX7E.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Select,
  Switch,
  Text,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-edit-7VMCKSEG.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditRegionSchema = objectType({
  name: stringType().min(1),
  currency_code: stringType(),
  payment_providers: arrayType(stringType()),
  automatic_taxes: booleanType(),
  is_tax_inclusive: booleanType()
});
var EditRegionForm = ({
  region,
  currencies: currencies2,
  paymentProviders,
  pricePreferences
}) => {
  var _a;
  const { t } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const pricePreferenceForRegion = pricePreferences == null ? void 0 : pricePreferences.find(
    (preference) => preference.attribute === "region_id" && preference.value === region.id
  );
  const form = useForm({
    defaultValues: {
      name: region.name,
      currency_code: region.currency_code.toUpperCase(),
      payment_providers: ((_a = region.payment_providers) == null ? void 0 : _a.map((pp) => pp.id)) || [],
      automatic_taxes: region.automatic_taxes,
      is_tax_inclusive: (pricePreferenceForRegion == null ? void 0 : pricePreferenceForRegion.is_tax_inclusive) || false
    }
  });
  const { mutateAsync: updateRegion, isPending: isPendingRegion } = useUpdateRegion(region.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    await updateRegion(
      {
        name: values.name,
        automatic_taxes: values.automatic_taxes,
        currency_code: values.currency_code.toLowerCase(),
        payment_providers: values.payment_providers,
        is_tax_inclusive: values.is_tax_inclusive
      },
      {
        onSuccess: () => {
          toast.success(t("regions.toast.edit"));
          handleSuccess();
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { onSubmit: handleSubmit, className: "flex flex-1 flex-col", children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "name",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.name") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "currency_code",
            render: ({ field: { onChange, ref, ...field } }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.currency") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(Select, { onValueChange: onChange, ...field, children: [
                  (0, import_jsx_runtime.jsx)(Select.Trigger, { ref, children: (0, import_jsx_runtime.jsx)(Select.Value, {}) }),
                  (0, import_jsx_runtime.jsx)(Select.Content, { children: currencies2.map((c) => (0, import_jsx_runtime.jsx)(Select.Item, { value: c.code, children: c.code.toUpperCase() }, c.code)) })
                ] }) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "automatic_taxes",
            render: ({ field: { value, onChange, ...field } }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between", children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.automaticTaxes") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Switch,
                    {
                      ...field,
                      checked: value,
                      onCheckedChange: onChange
                    }
                  ) })
                ] }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t("regions.automaticTaxesHint") }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] }) });
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "is_tax_inclusive",
            render: ({ field: { value, onChange, ...field } }) => {
              return (0, import_jsx_runtime.jsx)(Form.Item, { children: (0, import_jsx_runtime.jsxs)("div", { children: [
                (0, import_jsx_runtime.jsxs)("div", { className: "flex items-start justify-between", children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.taxInclusivePricing") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                    Switch,
                    {
                      ...field,
                      checked: value,
                      onCheckedChange: onChange
                    }
                  ) })
                ] }),
                (0, import_jsx_runtime.jsx)(Form.Hint, { children: t("regions.taxInclusiveHint") }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] }) });
            }
          }
        )
      ] }),
      (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
        (0, import_jsx_runtime.jsxs)("div", { children: [
          (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: "Providers" }),
          (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t("regions.providersHint") })
        ] }),
        (0, import_jsx_runtime.jsx)(
          Form.Field,
          {
            control: form.control,
            name: "payment_providers",
            render: ({ field }) => {
              return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                (0, import_jsx_runtime.jsx)(Form.Label, { children: t("fields.paymentProviders") }),
                (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                  Combobox,
                  {
                    options: paymentProviders.map((pp) => ({
                      label: formatProvider(pp.id),
                      value: pp.id
                    })),
                    ...field
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
              ] });
            }
          }
        )
      ] })
    ] }) }),
    (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t("actions.cancel") }) }),
      (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPendingRegion, children: t("actions.save") })
    ] }) })
  ] }) });
};
var RegionEdit = () => {
  const { t } = useTranslation();
  const { id } = useParams();
  const {
    region,
    isPending: isRegionLoading,
    isError: isRegionError,
    error: regionError
  } = useRegion(id, {
    fields: "*payment_providers,*countries,+automatic_taxes"
  });
  const {
    store,
    isPending: isStoreLoading,
    isError: isStoreError,
    error: storeError
  } = useStore();
  const {
    price_preferences: pricePreferences = [],
    isPending: isPreferenceLoading,
    isError: isPreferenceError,
    error: preferenceError
  } = usePricePreferences(
    {
      attribute: "region_id",
      value: id
    },
    { enabled: !!region }
  );
  const isLoading = isRegionLoading || isStoreLoading || isPreferenceLoading;
  const storeCurrencies = ((store == null ? void 0 : store.supported_currencies) ?? []).map(
    (c) => currencies[c.currency_code.toUpperCase()]
  );
  const { payment_providers: paymentProviders = [] } = usePaymentProviders({
    limit: 999,
    is_enabled: true
  });
  if (isRegionError) {
    throw regionError;
  }
  if (isStoreError) {
    throw storeError;
  }
  if (isPreferenceError) {
    throw preferenceError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t("regions.editRegion") }) }),
    !isLoading && region && (0, import_jsx_runtime2.jsx)(
      EditRegionForm,
      {
        region,
        currencies: storeCurrencies,
        paymentProviders,
        pricePreferences
      }
    )
  ] });
};
export {
  RegionEdit as Component
};
//# sourceMappingURL=region-edit-7VMCKSEG-HI6LG7LC.js.map
