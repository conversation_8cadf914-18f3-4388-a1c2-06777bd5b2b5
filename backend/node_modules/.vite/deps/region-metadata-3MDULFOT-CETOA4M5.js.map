{"version": 3, "sources": ["../../@medusajs/dashboard/dist/region-metadata-3MDULFOT.mjs"], "sourcesContent": ["import \"./chunk-XRTVFYCW.mjs\";\nimport {\n  MetadataForm\n} from \"./chunk-S2UEWRDA.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport {\n  useRegion,\n  useUpdateRegion\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/regions/region-metadata/region-metadata.tsx\nimport { useParams } from \"react-router-dom\";\nimport { jsx } from \"react/jsx-runtime\";\nvar RegionMetadata = () => {\n  const { id } = useParams();\n  const { region, isPending, isError, error } = useRegion(id);\n  const { mutateAsync, isPending: isMutating } = useUpdateRegion(id);\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteDrawer, { children: /* @__PURE__ */ jsx(\n    MetadataForm,\n    {\n      isPending,\n      isMutating,\n      hook: mutateAsync,\n      metadata: region?.metadata\n    }\n  ) });\n};\nexport {\n  RegionMetadata as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDA,yBAAoB;AACpB,IAAI,iBAAiB,MAAM;AACzB,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,QAAQ,WAAW,SAAS,MAAM,IAAI,UAAU,EAAE;AAC1D,QAAM,EAAE,aAAa,WAAW,WAAW,IAAI,gBAAgB,EAAE;AACjE,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,aAAa,EAAE,cAA0B;AAAA,IAClE;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN,UAAU,iCAAQ;AAAA,IACpB;AAAA,EACF,EAAE,CAAC;AACL;", "names": []}