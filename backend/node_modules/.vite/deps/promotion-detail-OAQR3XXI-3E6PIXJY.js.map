{"version": 3, "sources": ["../../@medusajs/dashboard/dist/promotion-detail-OAQR3XXI.mjs"], "sourcesContent": ["import {\n  formatPercentage\n} from \"./chunk-3WXBLS2P.mjs\";\nimport {\n  BadgeListSummary\n} from \"./chunk-BKJC5BGQ.mjs\";\nimport {\n  DateRangeDisplay\n} from \"./chunk-VAIHVNV6.mjs\";\nimport {\n  getPromotionStatus\n} from \"./chunk-JRIZAFLU.mjs\";\nimport {\n  formatCurrency\n} from \"./chunk-OV5NMSY6.mjs\";\nimport {\n  NoRecords\n} from \"./chunk-EMIHDNB7.mjs\";\nimport {\n  TwoColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport \"./chunk-4GQOUCX6.mjs\";\nimport \"./chunk-GWO5QQQW.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  TwoColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport {\n  promotionsQueryKeys,\n  useDeletePromotion,\n  usePromotion,\n  usePromotionRules\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/promotions/promotion-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar PromotionDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { promotion } = usePromotion(id, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!promotion) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: promotion.code });\n};\n\n// src/routes/promotions/promotion-detail/loader.ts\nvar promotionDetailQuery = (id) => ({\n  queryKey: promotionsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.promotion.retrieve(id)\n});\nvar promotionLoader = async ({ params }) => {\n  const id = params.id;\n  const query = promotionDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/promotions/promotion-detail/promotion-detail.tsx\nimport { useLoaderData, useParams as useParams2 } from \"react-router-dom\";\n\n// src/routes/promotions/promotion-detail/components/campaign-section/campaign-section.tsx\nimport { ArrowUpRightOnBox, PencilSquare } from \"@medusajs/icons\";\nimport { Container, Heading, Text } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar CampaignDetailSection = ({\n  campaign\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-3\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-muted flex items-center gap-x-1.5\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", className: \"text-ui-fg-base\", children: campaign.name }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", children: \"\\xB7\" }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", weight: \"plus\", children: campaign.campaign_identifier })\n    ] }),\n    /* @__PURE__ */ jsx2(\n      DateRangeDisplay,\n      {\n        startsAt: campaign.starts_at,\n        endsAt: campaign.ends_at,\n        showTime: true\n      }\n    )\n  ] });\n};\nvar CampaignSection = ({\n  campaign\n}) => {\n  const { t } = useTranslation();\n  const { id } = useParams();\n  const actions = [\n    {\n      label: t(\"actions.edit\"),\n      to: \"add-to-campaign\",\n      icon: /* @__PURE__ */ jsx2(PencilSquare, {})\n    }\n  ];\n  if (campaign) {\n    actions.unshift({\n      label: t(\"promotions.campaign.actions.goToCampaign\"),\n      to: `/campaigns/${campaign.id}`,\n      icon: /* @__PURE__ */ jsx2(ArrowUpRightOnBox, {})\n    });\n  }\n  return /* @__PURE__ */ jsxs(Container, { children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n      /* @__PURE__ */ jsx2(Heading, { level: \"h2\", children: t(\"promotions.fields.campaign\") }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions\n            }\n          ]\n        }\n      )\n    ] }),\n    campaign ? /* @__PURE__ */ jsx2(CampaignDetailSection, { campaign }) : /* @__PURE__ */ jsx2(\n      NoRecords,\n      {\n        className: \"h-[180px] pt-4 text-center\",\n        title: \"Not part of a campaign\",\n        message: \"Add this promotion to an existing campaign\",\n        action: {\n          to: `/promotions/${id}/add-to-campaign`,\n          label: \"Add to Campaign\"\n        },\n        buttonVariant: \"transparentIconLeft\"\n      }\n    )\n  ] });\n};\n\n// src/routes/promotions/promotion-detail/components/promotion-conditions-section/promotion-conditions-section.tsx\nimport { PencilSquare as PencilSquare2 } from \"@medusajs/icons\";\nimport { Badge, Container as Container2, Heading as Heading2 } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { jsx as jsx3, jsxs as jsxs2 } from \"react/jsx-runtime\";\nfunction RuleBlock({ rule }) {\n  return /* @__PURE__ */ jsx3(\"div\", { className: \"bg-ui-bg-subtle shadow-borders-base align-center flex justify-around rounded-md p-2\", children: /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle txt-compact-xsmall flex items-center whitespace-nowrap\", children: [\n    /* @__PURE__ */ jsx3(\n      Badge,\n      {\n        size: \"2xsmall\",\n        className: \"txt-compact-xsmall-plus tag-neutral-text mx-1 inline-block truncate\",\n        children: rule.attribute_label\n      },\n      \"rule-attribute\"\n    ),\n    /* @__PURE__ */ jsx3(\"span\", { className: \"txt-compact-2xsmall mx-1 inline-block\", children: rule.operator_label }),\n    /* @__PURE__ */ jsx3(\n      BadgeListSummary,\n      {\n        inline: true,\n        className: \"!txt-compact-small-plus\",\n        list: rule.field_type === \"number\" ? [rule.values] : rule.values?.map((v) => v.label)\n      }\n    )\n  ] }) });\n}\nvar PromotionConditionsSection = ({\n  rules,\n  ruleType\n}) => {\n  const { t } = useTranslation2();\n  return /* @__PURE__ */ jsxs2(Container2, { className: \"p-0\", children: [\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx3(\"div\", { className: \"flex flex-col\", children: /* @__PURE__ */ jsx3(Heading2, { children: t(`promotions.fields.conditions.${ruleType}.title`) }) }),\n      /* @__PURE__ */ jsx3(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx3(PencilSquare2, {}),\n                  label: t(\"actions.edit\"),\n                  to: `${ruleType}/edit`\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs2(\"div\", { className: \"text-ui-fg-subtle flex flex-col gap-2 px-6 pb-4 pt-2\", children: [\n      !rules.length && /* @__PURE__ */ jsx3(\n        NoRecords,\n        {\n          className: \"h-[180px]\",\n          title: t(\"general.noRecordsTitle\"),\n          message: t(\"promotions.conditions.list.noRecordsMessage\"),\n          action: {\n            to: `${ruleType}/edit`,\n            label: t(\"promotions.conditions.add\")\n          },\n          buttonVariant: \"transparentIconLeft\"\n        }\n      ),\n      rules.map((rule) => /* @__PURE__ */ jsx3(RuleBlock, { rule }, `${rule.id}-${rule.attribute}`))\n    ] })\n  ] });\n};\n\n// src/routes/promotions/promotion-detail/components/promotion-general-section/promotion-general-section.tsx\nimport { PencilSquare as PencilSquare3, Trash } from \"@medusajs/icons\";\nimport {\n  Badge as Badge2,\n  Container as Container3,\n  Copy,\n  Heading as Heading3,\n  StatusBadge,\n  Text as Text2,\n  usePrompt\n} from \"@medusajs/ui\";\nimport { useTranslation as useTranslation3 } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx4, jsxs as jsxs3 } from \"react/jsx-runtime\";\nfunction getDisplayValue(promotion) {\n  const value = promotion.application_method?.value;\n  if (!value) {\n    return null;\n  }\n  if (promotion.application_method?.type === \"fixed\") {\n    const currency = promotion.application_method?.currency_code;\n    if (!currency) {\n      return null;\n    }\n    return formatCurrency(value, currency);\n  } else if (promotion.application_method?.type === \"percentage\") {\n    return formatPercentage(value);\n  }\n  return null;\n}\nvar PromotionGeneralSection = ({\n  promotion\n}) => {\n  const { t } = useTranslation3();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeletePromotion(promotion.id);\n  const handleDelete = async () => {\n    const confirm = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"promotions.deleteWarning\", {\n        code: promotion.code\n      }),\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      verificationText: promotion.code,\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!confirm) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        navigate(\"/promotions\", { replace: true });\n      }\n    });\n  };\n  const [color, text] = getPromotionStatus(promotion);\n  const displayValue = getDisplayValue(promotion);\n  return /* @__PURE__ */ jsxs3(Container3, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(\"div\", { className: \"flex flex-col\", children: /* @__PURE__ */ jsx4(Heading3, { children: promotion.code }) }),\n      /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx4(StatusBadge, { color, children: text }),\n        /* @__PURE__ */ jsx4(\n          ActionMenu,\n          {\n            groups: [\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx4(PencilSquare3, {}),\n                    label: t(\"actions.edit\"),\n                    to: `/promotions/${promotion.id}/edit`\n                  }\n                ]\n              },\n              {\n                actions: [\n                  {\n                    icon: /* @__PURE__ */ jsx4(Trash, {}),\n                    label: t(\"actions.delete\"),\n                    onClick: handleDelete\n                  }\n                ]\n              }\n            ]\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"promotions.fields.campaign\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", className: \"text-pretty\", children: promotion.is_automatic ? t(\"promotions.form.method.automatic.title\") : t(\"promotions.form.method.code.title\") })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"fields.code\") }),\n      /* @__PURE__ */ jsx4(\n        Copy,\n        {\n          content: promotion.code,\n          className: \"text-ui-tag-neutral-text\",\n          asChild: true,\n          children: /* @__PURE__ */ jsx4(\n            Badge2,\n            {\n              size: \"2xsmall\",\n              rounded: \"full\",\n              className: \"cursor-pointer text-pretty\",\n              children: promotion.code\n            }\n          )\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"promotions.fields.type\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", className: \"text-pretty capitalize\", children: promotion.type })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"promotions.fields.value\") }),\n      /* @__PURE__ */ jsxs3(\"div\", { className: \"flex items-center gap-x-2\", children: [\n        /* @__PURE__ */ jsx4(Text2, { className: \"inline\", size: \"small\", leading: \"compact\", children: displayValue || \"-\" }),\n        promotion?.application_method?.type === \"fixed\" && /* @__PURE__ */ jsx4(Badge2, { size: \"2xsmall\", rounded: \"full\", children: promotion?.application_method?.currency_code?.toUpperCase() })\n      ] })\n    ] }),\n    /* @__PURE__ */ jsxs3(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4\", children: [\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", weight: \"plus\", leading: \"compact\", children: t(\"promotions.fields.allocation\") }),\n      /* @__PURE__ */ jsx4(Text2, { size: \"small\", leading: \"compact\", className: \"text-pretty capitalize\", children: promotion.application_method?.allocation })\n    ] })\n  ] });\n};\n\n// src/routes/promotions/promotion-detail/promotion-detail.tsx\nimport { jsx as jsx5, jsxs as jsxs4 } from \"react/jsx-runtime\";\nvar PromotionDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams2();\n  const { promotion, isLoading } = usePromotion(id, { initialData });\n  const query = {};\n  if (promotion?.type === \"buyget\") {\n    query.promotion_type = promotion.type;\n  }\n  const { rules } = usePromotionRules(id, \"rules\", query);\n  const { rules: targetRules } = usePromotionRules(id, \"target-rules\", query);\n  const { rules: buyRules } = usePromotionRules(id, \"buy-rules\", query);\n  const { getWidgets } = useExtension();\n  if (isLoading || !promotion) {\n    return /* @__PURE__ */ jsx5(TwoColumnPageSkeleton, { mainSections: 3, sidebarSections: 1, showJSON: true });\n  }\n  return /* @__PURE__ */ jsxs4(\n    TwoColumnPage,\n    {\n      data: promotion,\n      widgets: {\n        after: getWidgets(\"promotion.details.after\"),\n        before: getWidgets(\"promotion.details.before\"),\n        sideAfter: getWidgets(\"promotion.details.side.after\"),\n        sideBefore: getWidgets(\"promotion.details.side.before\")\n      },\n      hasOutlet: true,\n      showJSON: true,\n      children: [\n        /* @__PURE__ */ jsxs4(TwoColumnPage.Main, { children: [\n          /* @__PURE__ */ jsx5(PromotionGeneralSection, { promotion }),\n          /* @__PURE__ */ jsx5(PromotionConditionsSection, { rules: rules || [], ruleType: \"rules\" }),\n          /* @__PURE__ */ jsx5(\n            PromotionConditionsSection,\n            {\n              rules: targetRules || [],\n              ruleType: \"target-rules\"\n            }\n          ),\n          promotion.type === \"buyget\" && /* @__PURE__ */ jsx5(\n            PromotionConditionsSection,\n            {\n              rules: buyRules || [],\n              ruleType: \"buy-rules\"\n            }\n          )\n        ] }),\n        /* @__PURE__ */ jsx5(TwoColumnPage.Sidebar, { children: /* @__PURE__ */ jsx5(CampaignSection, { campaign: promotion.campaign }) })\n      ]\n    }\n  );\n};\nexport {\n  PromotionDetailBreadcrumb as Breadcrumb,\n  PromotionDetail as Component,\n  promotionLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,yBAAoB;AAgCpB,IAAAA,sBAAkC;AAyElC,IAAAC,sBAA2C;AAgF3C,IAAAC,sBAA2C;AAyH3C,IAAAA,sBAA2C;AAjT3C,IAAI,4BAA4B,CAAC,UAAU;AACzC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,UAAU,IAAI,aAAa,IAAI;AAAA,IACrC,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,UAAU,KAAK,CAAC;AACjE;AAGA,IAAI,uBAAuB,CAAC,QAAQ;AAAA,EAClC,UAAU,oBAAoB,OAAO,EAAE;AAAA,EACvC,SAAS,YAAY,IAAI,MAAM,UAAU,SAAS,EAAE;AACtD;AACA,IAAI,kBAAkB,OAAO,EAAE,OAAO,MAAM;AAC1C,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,qBAAqB,EAAE;AACrC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAWA,IAAI,wBAAwB,CAAC;AAAA,EAC3B;AACF,MAAM;AACJ,aAAuB,0BAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,QACjE,0BAAK,OAAO,EAAE,WAAW,gDAAgD,UAAU;AAAA,UACjF,oBAAAC,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,WAAW,mBAAmB,UAAU,SAAS,KAAK,CAAC;AAAA,UACnG,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,UAAU,IAAO,CAAC;AAAA,UAC9D,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,QAAQ,QAAQ,UAAU,SAAS,oBAAoB,CAAC;AAAA,IACtG,EAAE,CAAC;AAAA,QACa,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,UAAU,SAAS;AAAA,QACnB,QAAQ,SAAS;AAAA,QACjB,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,kBAAkB,CAAC;AAAA,EACrB;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,UAAU;AAAA,IACd;AAAA,MACE,OAAO,EAAE,cAAc;AAAA,MACvB,IAAI;AAAA,MACJ,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,UAAU;AACZ,YAAQ,QAAQ;AAAA,MACd,OAAO,EAAE,0CAA0C;AAAA,MACnD,IAAI,cAAc,SAAS,EAAE;AAAA,MAC7B,UAAsB,oBAAAA,KAAK,mBAAmB,CAAC,CAAC;AAAA,IAClD,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,WAAW,EAAE,UAAU;AAAA,QACjC,0BAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,UACtE,oBAAAA,KAAK,SAAS,EAAE,OAAO,MAAM,UAAU,EAAE,4BAA4B,EAAE,CAAC;AAAA,UACxE,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,IACH,eAA2B,oBAAAA,KAAK,uBAAuB,EAAE,SAAS,CAAC,QAAoB,oBAAAA;AAAA,MACrF;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,IAAI,eAAe,EAAE;AAAA,UACrB,OAAO;AAAA,QACT;AAAA,QACA,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAOA,SAAS,UAAU,EAAE,KAAK,GAAG;AAvL7B;AAwLE,aAAuB,oBAAAC,KAAK,OAAO,EAAE,WAAW,uFAAuF,cAA0B,oBAAAC,MAAM,OAAO,EAAE,WAAW,4EAA4E,UAAU;AAAA,QAC/P,oBAAAD;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,IACF;AAAA,QACgB,oBAAAA,KAAK,QAAQ,EAAE,WAAW,yCAAyC,UAAU,KAAK,eAAe,CAAC;AAAA,QAClG,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,MAAM,KAAK,eAAe,WAAW,CAAC,KAAK,MAAM,KAAI,UAAK,WAAL,mBAAa,IAAI,CAAC,MAAM,EAAE;AAAA,MACjF;AAAA,IACF;AAAA,EACF,EAAE,CAAC,EAAE,CAAC;AACR;AACA,IAAI,6BAA6B,CAAC;AAAA,EAChC;AAAA,EACA;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,aAAuB,oBAAAC,MAAM,WAAY,EAAE,WAAW,OAAO,UAAU;AAAA,QACrD,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAD,KAAK,OAAO,EAAE,WAAW,iBAAiB,cAA0B,oBAAAA,KAAK,SAAU,EAAE,UAAU,EAAE,gCAAgC,QAAQ,QAAQ,EAAE,CAAC,EAAE,CAAC;AAAA,UACvJ,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,kBAC5C,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI,GAAG,QAAQ;AAAA,gBACjB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAC,MAAM,OAAO,EAAE,WAAW,wDAAwD,UAAU;AAAA,MAC1G,CAAC,MAAM,cAA0B,oBAAAD;AAAA,QAC/B;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,EAAE,wBAAwB;AAAA,UACjC,SAAS,EAAE,6CAA6C;AAAA,UACxD,QAAQ;AAAA,YACN,IAAI,GAAG,QAAQ;AAAA,YACf,OAAO,EAAE,2BAA2B;AAAA,UACtC;AAAA,UACA,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,MACA,MAAM,IAAI,CAAC,aAAyB,oBAAAA,KAAK,WAAW,EAAE,KAAK,GAAG,GAAG,KAAK,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;AAAA,IAC/F,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAgBA,SAAS,gBAAgB,WAAW;AAvQpC;AAwQE,QAAM,SAAQ,eAAU,uBAAV,mBAA8B;AAC5C,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,QAAI,eAAU,uBAAV,mBAA8B,UAAS,SAAS;AAClD,UAAM,YAAW,eAAU,uBAAV,mBAA8B;AAC/C,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AACA,WAAO,eAAe,OAAO,QAAQ;AAAA,EACvC,aAAW,eAAU,uBAAV,mBAA8B,UAAS,cAAc;AAC9D,WAAO,iBAAiB,KAAK;AAAA,EAC/B;AACA,SAAO;AACT;AACA,IAAI,0BAA0B,CAAC;AAAA,EAC7B;AACF,MAAM;AAzRN;AA0RE,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,mBAAmB,UAAU,EAAE;AACvD,QAAM,eAAe,YAAY;AAC/B,UAAM,UAAU,MAAM,OAAO;AAAA,MAC3B,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,4BAA4B;AAAA,QACzC,MAAM,UAAU;AAAA,MAClB,CAAC;AAAA,MACD,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,kBAAkB,UAAU;AAAA,MAC5B,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,iBAAS,eAAe,EAAE,SAAS,KAAK,CAAC;AAAA,MAC3C;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,CAAC,OAAO,IAAI,IAAI,mBAAmB,SAAS;AAClD,QAAM,eAAe,gBAAgB,SAAS;AAC9C,aAAuB,oBAAAE,MAAM,WAAY,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC9D,oBAAAA,MAAM,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UACjF,oBAAAC,KAAK,OAAO,EAAE,WAAW,iBAAiB,cAA0B,oBAAAA,KAAK,SAAU,EAAE,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC;AAAA,UAClH,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,aAAa,EAAE,OAAO,UAAU,KAAK,CAAC;AAAA,YAC3C,oBAAAA;AAAA,UACd;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,cAAe,CAAC,CAAC;AAAA,oBAC5C,OAAO,EAAE,cAAc;AAAA,oBACvB,IAAI,eAAe,UAAU,EAAE;AAAA,kBACjC;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,kBACP;AAAA,oBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,oBACpC,OAAO,EAAE,gBAAgB;AAAA,oBACzB,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,UAC9F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,4BAA4B,EAAE,CAAC;AAAA,UAC5G,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,eAAe,UAAU,UAAU,eAAe,EAAE,wCAAwC,IAAI,EAAE,mCAAmC,EAAE,CAAC;AAAA,IACtN,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC/F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,aAAa,EAAE,CAAC;AAAA,UAC7F,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,SAAS,UAAU;AAAA,UACnB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,cAA0B,oBAAAA;AAAA,YACxB;AAAA,YACA;AAAA,cACE,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW;AAAA,cACX,UAAU,UAAU;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,UAC9F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,wBAAwB,EAAE,CAAC;AAAA,UACxG,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,0BAA0B,UAAU,UAAU,KAAK,CAAC;AAAA,IAClI,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,UAC9F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,yBAAyB,EAAE,CAAC;AAAA,UACzG,oBAAAD,MAAM,OAAO,EAAE,WAAW,6BAA6B,UAAU;AAAA,YAC/D,oBAAAC,KAAK,MAAO,EAAE,WAAW,UAAU,MAAM,SAAS,SAAS,WAAW,UAAU,gBAAgB,IAAI,CAAC;AAAA,UACrH,4CAAW,uBAAX,mBAA+B,UAAS,eAA2B,oBAAAA,KAAK,OAAQ,EAAE,MAAM,WAAW,SAAS,QAAQ,WAAU,kDAAW,uBAAX,mBAA+B,kBAA/B,mBAA8C,cAAc,CAAC;AAAA,MAC7L,EAAE,CAAC;AAAA,IACL,EAAE,CAAC;AAAA,QACa,oBAAAD,MAAM,OAAO,EAAE,WAAW,4DAA4D,UAAU;AAAA,UAC9F,oBAAAC,KAAK,MAAO,EAAE,MAAM,SAAS,QAAQ,QAAQ,SAAS,WAAW,UAAU,EAAE,8BAA8B,EAAE,CAAC;AAAA,UAC9G,oBAAAA,KAAK,MAAO,EAAE,MAAM,SAAS,SAAS,WAAW,WAAW,0BAA0B,WAAU,eAAU,uBAAV,mBAA8B,WAAW,CAAC;AAAA,IAC5J,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAIA,IAAI,kBAAkB,MAAM;AAC1B,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAW;AAC1B,QAAM,EAAE,WAAW,UAAU,IAAI,aAAa,IAAI,EAAE,YAAY,CAAC;AACjE,QAAM,QAAQ,CAAC;AACf,OAAI,uCAAW,UAAS,UAAU;AAChC,UAAM,iBAAiB,UAAU;AAAA,EACnC;AACA,QAAM,EAAE,MAAM,IAAI,kBAAkB,IAAI,SAAS,KAAK;AACtD,QAAM,EAAE,OAAO,YAAY,IAAI,kBAAkB,IAAI,gBAAgB,KAAK;AAC1E,QAAM,EAAE,OAAO,SAAS,IAAI,kBAAkB,IAAI,aAAa,KAAK;AACpE,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,WAAW;AAC3B,eAAuB,oBAAAC,KAAK,uBAAuB,EAAE,cAAc,GAAG,iBAAiB,GAAG,UAAU,KAAK,CAAC;AAAA,EAC5G;AACA,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,SAAS;AAAA,QACP,OAAO,WAAW,yBAAyB;AAAA,QAC3C,QAAQ,WAAW,0BAA0B;AAAA,QAC7C,WAAW,WAAW,8BAA8B;AAAA,QACpD,YAAY,WAAW,+BAA+B;AAAA,MACxD;AAAA,MACA,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,oBAAAA,MAAM,cAAc,MAAM,EAAE,UAAU;AAAA,cACpC,oBAAAD,KAAK,yBAAyB,EAAE,UAAU,CAAC;AAAA,cAC3C,oBAAAA,KAAK,4BAA4B,EAAE,OAAO,SAAS,CAAC,GAAG,UAAU,QAAQ,CAAC;AAAA,cAC1E,oBAAAA;AAAA,YACd;AAAA,YACA;AAAA,cACE,OAAO,eAAe,CAAC;AAAA,cACvB,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,UAAU,SAAS,gBAA4B,oBAAAA;AAAA,YAC7C;AAAA,YACA;AAAA,cACE,OAAO,YAAY,CAAC;AAAA,cACpB,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,EAAE,CAAC;AAAA,YACa,oBAAAA,KAAK,cAAc,SAAS,EAAE,cAA0B,oBAAAA,KAAK,iBAAiB,EAAE,UAAU,UAAU,SAAS,CAAC,EAAE,CAAC;AAAA,MACnI;AAAA,IACF;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "jsx2", "jsx3", "jsxs2", "jsxs3", "jsx4", "jsx5", "jsxs4"]}