import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import {
  useSalesChannelTableColumns,
  useSalesChannelTableEmptyState,
  useSalesChannelTableFilters,
  useSalesChannelTableQuery
} from "./chunk-OKLKW4MW.js";
import "./chunk-ZMX3GMKA.js";
import {
  DataTable
} from "./chunk-CHHMXAMR.js";
import "./chunk-32T72GVU.js";
import "./chunk-WP4SM64O.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import {
  useStore
} from "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import {
  useDeleteSalesChannelLazy,
  useSalesChannels
} from "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  Trash,
  createDataTableColumnHelper,
  toast,
  usePrompt
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/sales-channel-list-45WEQT3Q.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var SalesChannelListTable = () => {
  const { t } = useTranslation();
  const { store } = useStore();
  const searchParams = useSalesChannelTableQuery({
    pageSize: PAGE_SIZE
  });
  const { sales_channels, count, isPending, isError, error } = useSalesChannels(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useSalesChannelTableFilters();
  const emptyState = useSalesChannelTableEmptyState();
  const sales_channels_data = (sales_channels == null ? void 0 : sales_channels.map((sales_channel) => {
    return {
      ...sales_channel,
      is_default: (store == null ? void 0 : store.default_sales_channel_id) === sales_channel.id
    };
  })) ?? [];
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "p-0", children: (0, import_jsx_runtime.jsx)(
    DataTable,
    {
      data: sales_channels_data,
      columns,
      rowCount: count,
      getRowId: (row) => row.id,
      pageSize: PAGE_SIZE,
      filters,
      isLoading: isPending,
      emptyState,
      heading: t("salesChannels.domain"),
      subHeading: t("salesChannels.subtitle"),
      action: {
        label: t("actions.create"),
        to: "/settings/sales-channels/create"
      },
      rowHref: (row) => `/settings/sales-channels/${row.id}`
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const navigate = useNavigate();
  const base = useSalesChannelTableColumns();
  const { mutateAsync } = useDeleteSalesChannelLazy();
  const handleDelete = (0, import_react.useCallback)(
    async (salesChannel) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("salesChannels.deleteSalesChannelWarning", {
          name: salesChannel.name
        }),
        verificationInstruction: t("general.typeToConfirm"),
        verificationText: salesChannel.name,
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel")
      });
      if (!confirm) {
        return;
      }
      await mutateAsync(salesChannel.id, {
        onSuccess: () => {
          toast.success(t("salesChannels.toast.delete"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      });
    },
    [t, prompt, mutateAsync]
  );
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.action({
        actions: (ctx) => {
          const disabledTooltip = ctx.row.original.is_default ? t("salesChannels.tooltip.cannotDeleteDefault") : void 0;
          return [
            [
              {
                icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                label: t("actions.edit"),
                onClick: () => navigate(
                  `/settings/sales-channels/${ctx.row.original.id}/edit`
                )
              }
            ],
            [
              {
                icon: (0, import_jsx_runtime.jsx)(Trash, {}),
                label: t("actions.delete"),
                onClick: () => handleDelete(ctx.row.original),
                disabled: ctx.row.original.is_default,
                disabledTooltip
              }
            ]
          ];
        }
      })
    ],
    [base, handleDelete, navigate, t]
  );
};
var SalesChannelList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("sales_channel.list.before"),
        after: getWidgets("sales_channel.list.after")
      },
      hasOutlet: true,
      children: (0, import_jsx_runtime2.jsx)(SalesChannelListTable, {})
    }
  );
};
export {
  SalesChannelList as Component
};
//# sourceMappingURL=sales-channel-list-45WEQT3Q-FAVGA3BO.js.map
