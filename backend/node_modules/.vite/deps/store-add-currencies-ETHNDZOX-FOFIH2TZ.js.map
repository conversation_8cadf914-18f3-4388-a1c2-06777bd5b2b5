{"version": 3, "sources": ["../../@medusajs/dashboard/dist/store-add-currencies-ETHNDZOX.mjs"], "sourcesContent": ["import {\n  useCurrenciesTableColumns,\n  useCurrenciesTableQuery\n} from \"./chunk-NEZX6265.mjs\";\nimport \"./chunk-MSDRGCRR.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport \"./chunk-P3UUX2T6.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-B2JT2FOA.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-GJUPECDU.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCurrencies\n} from \"./chunk-C5LYZZZ5.mjs\";\nimport {\n  useStore,\n  useUpdateStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  pricePreferencesQueryKeys,\n  usePricePreferences\n} from \"./chunk-QL4XKIVL.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/store/store-add-currencies/components/add-currencies-form/add-currencies-form.tsx\nimport { Button, Checkbox, Hint, Switch, toast, Tooltip } from \"@medusajs/ui\";\nimport {\n  createColumnHelper\n} from \"@tanstack/react-table\";\nimport { useCallback, useMemo, useState } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useForm } from \"react-hook-form\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar AddCurrenciesSchema = zod.object({\n  currencies: zod.array(zod.string()).min(1),\n  pricePreferences: zod.record(zod.boolean())\n});\nvar PAGE_SIZE = 50;\nvar PREFIX = \"ac\";\nvar AddCurrenciesForm = ({\n  store,\n  pricePreferences\n}) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const { raw, searchParams } = useCurrenciesTableQuery({\n    pageSize: 50,\n    prefix: PREFIX\n  });\n  const {\n    currencies,\n    count,\n    isPending: isLoading,\n    isError,\n    error\n  } = useCurrencies(searchParams, {\n    placeholderData: keepPreviousData\n  });\n  const form = useForm({\n    defaultValues: {\n      currencies: [],\n      pricePreferences: pricePreferences?.reduce(\n        (acc, curr) => {\n          if (curr.value) {\n            acc[curr.value] = curr.is_tax_inclusive;\n          }\n          return acc;\n        },\n        {}\n      )\n    },\n    resolver: zodResolver(AddCurrenciesSchema)\n  });\n  const [rowSelection, setRowSelection] = useState({});\n  const { setValue, watch } = form;\n  const pricePreferenceValues = watch(\"pricePreferences\");\n  const updater = (fn) => {\n    const updated = typeof fn === \"function\" ? fn(rowSelection) : fn;\n    const ids = Object.keys(updated);\n    setValue(\"currencies\", ids, {\n      shouldDirty: true,\n      shouldTouch: true\n    });\n    setRowSelection(updated);\n  };\n  const preSelectedRows = store.supported_currencies?.map((c) => c.currency_code) ?? [];\n  const setPricePreferences = useCallback(\n    (values) => {\n      setValue(\"pricePreferences\", values);\n    },\n    [setValue]\n  );\n  const columns = useColumns(pricePreferenceValues, setPricePreferences);\n  const { table } = useDataTable({\n    data: currencies ?? [],\n    columns,\n    count,\n    getRowId: (row) => row.code,\n    enableRowSelection: (row) => !preSelectedRows.includes(row.original.code),\n    enablePagination: true,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX,\n    rowSelection: {\n      state: rowSelection,\n      updater\n    }\n  });\n  const { mutateAsync, isPending } = useUpdateStore(store.id);\n  const handleSubmit = form.handleSubmit(async (data) => {\n    const currencies2 = Array.from(\n      /* @__PURE__ */ new Set([...data.currencies, ...preSelectedRows])\n    );\n    let defaultCurrency = store.supported_currencies?.find(\n      (c) => c.is_default\n    )?.currency_code;\n    if (!currencies2.includes(defaultCurrency ?? \"\")) {\n      defaultCurrency = currencies2?.[0];\n    }\n    await mutateAsync(\n      {\n        supported_currencies: currencies2.map((c) => ({\n          currency_code: c,\n          is_default: c === defaultCurrency,\n          is_tax_inclusive: data.pricePreferences[c]\n        }))\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"store.toast.currenciesUpdated\"));\n          queryClient.invalidateQueries({\n            queryKey: pricePreferencesQueryKeys.all\n          });\n          handleSuccess();\n        },\n        onError: (error2) => {\n          toast.error(error2.message);\n        }\n      }\n    );\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 items-center justify-between\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center\", children: form.formState.errors.currencies && /* @__PURE__ */ jsx(Hint, { variant: \"error\", children: form.formState.errors.currencies.message }) }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\n          _DataTable,\n          {\n            table,\n            pageSize: PAGE_SIZE,\n            count,\n            columns,\n            layout: \"fill\",\n            pagination: true,\n            search: \"autofocus\",\n            prefix: PREFIX,\n            orderBy: [\n              { key: \"name\", label: t(\"fields.name\") },\n              { key: \"code\", label: t(\"fields.code\") }\n            ],\n            isLoading,\n            queryObject: raw\n          }\n        ) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = (pricePreferences, setPricePreferences) => {\n  const { t } = useTranslation();\n  const base = useCurrenciesTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isSelected = row.getIsSelected() || isPreSelected;\n          const Component = /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: isSelected,\n              disabled: isPreSelected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n          if (isPreSelected) {\n            return /* @__PURE__ */ jsx(Tooltip, { content: t(\"store.currencyAlreadyAdded\"), side: \"right\", children: Component });\n          }\n          return Component;\n        }\n      }),\n      ...base,\n      columnHelper.display({\n        id: \"select\",\n        header: () => /* @__PURE__ */ jsx(\"div\", { className: \"whitespace-nowrap\", children: t(\"fields.taxInclusivePricing\") }),\n        cell: ({ row }) => {\n          const isPreSelected = !row.getCanSelect();\n          const isTaxInclusive = pricePreferences[row.original.code];\n          return /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end\", children: /* @__PURE__ */ jsx(\n            Switch,\n            {\n              disabled: isPreSelected,\n              checked: isTaxInclusive ?? false,\n              onCheckedChange: (val) => {\n                setPricePreferences({\n                  ...pricePreferences,\n                  [row.original.code]: val\n                });\n              }\n            }\n          ) });\n        }\n      })\n    ],\n    [t, base, pricePreferences, setPricePreferences]\n  );\n};\n\n// src/routes/store/store-add-currencies/store-add-currencies.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar StoreAddCurrencies = () => {\n  const { store, isPending, isError, error } = useStore();\n  const {\n    price_preferences: pricePreferences,\n    isPending: isPricePreferencesPending,\n    isError: isPricePreferencesError,\n    error: pricePreferencesError\n  } = usePricePreferences(\n    {\n      attribute: \"currency_code\",\n      value: store?.supported_currencies?.map((c) => c.currency_code)\n    },\n    {\n      enabled: !!store\n    }\n  );\n  const ready = !!store && !isPending && !!pricePreferences && !isPricePreferencesPending;\n  if (isError) {\n    throw error;\n  }\n  if (isPricePreferencesError) {\n    throw pricePreferencesError;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: ready && /* @__PURE__ */ jsx2(AddCurrenciesForm, { store, pricePreferences }) });\n};\nexport {\n  StoreAddCurrencies as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDA,mBAA+C;AAM/C,yBAA0B;AAkN1B,IAAAA,sBAA4B;AAjN5B,IAAI,sBAA0B,WAAO;AAAA,EACnC,YAAgB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAAA,EACzC,kBAAsB,WAAW,YAAQ,CAAC;AAC5C,CAAC;AACD,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,oBAAoB,CAAC;AAAA,EACvB;AAAA,EACA;AACF,MAAM;AArEN;AAsEE,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,EAAE,KAAK,aAAa,IAAI,wBAAwB;AAAA,IACpD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,cAAc,cAAc;AAAA,IAC9B,iBAAiB;AAAA,EACnB,CAAC;AACD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,YAAY,CAAC;AAAA,MACb,kBAAkB,qDAAkB;AAAA,QAClC,CAAC,KAAK,SAAS;AACb,cAAI,KAAK,OAAO;AACd,gBAAI,KAAK,KAAK,IAAI,KAAK;AAAA,UACzB;AACA,iBAAO;AAAA,QACT;AAAA,QACA,CAAC;AAAA;AAAA,IAEL;AAAA,IACA,UAAU,EAAY,mBAAmB;AAAA,EAC3C,CAAC;AACD,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,UAAU,MAAM,IAAI;AAC5B,QAAM,wBAAwB,MAAM,kBAAkB;AACtD,QAAM,UAAU,CAAC,OAAO;AACtB,UAAM,UAAU,OAAO,OAAO,aAAa,GAAG,YAAY,IAAI;AAC9D,UAAM,MAAM,OAAO,KAAK,OAAO;AAC/B,aAAS,cAAc,KAAK;AAAA,MAC1B,aAAa;AAAA,MACb,aAAa;AAAA,IACf,CAAC;AACD,oBAAgB,OAAO;AAAA,EACzB;AACA,QAAM,oBAAkB,WAAM,yBAAN,mBAA4B,IAAI,CAAC,MAAM,EAAE,mBAAkB,CAAC;AACpF,QAAM,0BAAsB;AAAA,IAC1B,CAAC,WAAW;AACV,eAAS,oBAAoB,MAAM;AAAA,IACrC;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACA,QAAM,UAAU,WAAW,uBAAuB,mBAAmB;AACrE,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,SAAS,IAAI,SAAS,IAAI;AAAA,IACxE,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,cAAc;AAAA,MACZ,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,eAAe,MAAM,EAAE;AAC1D,QAAM,eAAe,KAAK,aAAa,OAAO,SAAS;AAvIzD,QAAAC,KAAA;AAwII,UAAM,cAAc,MAAM;AAAA,MACR,oBAAI,IAAI,CAAC,GAAG,KAAK,YAAY,GAAG,eAAe,CAAC;AAAA,IAClE;AACA,QAAI,mBAAkB,MAAAA,MAAA,MAAM,yBAAN,gBAAAA,IAA4B;AAAA,MAChD,CAAC,MAAM,EAAE;AAAA,UADW,mBAEnB;AACH,QAAI,CAAC,YAAY,SAAS,mBAAmB,EAAE,GAAG;AAChD,wBAAkB,2CAAc;AAAA,IAClC;AACA,UAAM;AAAA,MACJ;AAAA,QACE,sBAAsB,YAAY,IAAI,CAAC,OAAO;AAAA,UAC5C,eAAe;AAAA,UACf,YAAY,MAAM;AAAA,UAClB,kBAAkB,KAAK,iBAAiB,CAAC;AAAA,QAC3C,EAAE;AAAA,MACJ;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQD,GAAE,+BAA+B,CAAC;AAChD,sBAAY,kBAAkB;AAAA,YAC5B,UAAU,0BAA0B;AAAA,UACtC,CAAC;AACD,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,WAAW;AACnB,gBAAM,MAAM,OAAO,OAAO;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,wBAAI,OAAO,EAAE,WAAW,4CAA4C,cAA0B,wBAAI,OAAO,EAAE,WAAW,qBAAqB,UAAU,KAAK,UAAU,OAAO,kBAA8B,wBAAI,MAAM,EAAE,SAAS,SAAS,UAAU,KAAK,UAAU,OAAO,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YAC5V,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B;AAAA,UACvH;AAAA,UACA;AAAA,YACE;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,EAAE,KAAK,QAAQ,OAAOA,GAAE,aAAa,EAAE;AAAA,cACvC,EAAE,KAAK,QAAQ,OAAOA,GAAE,aAAa,EAAE;AAAA,YACzC;AAAA,YACA;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF,EAAE,CAAC;AAAA,YACa,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,CAAC,kBAAkB,wBAAwB;AAC1D,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAe;AAC7B,QAAM,OAAO,0BAA0B;AACvC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,aAAa,IAAI,cAAc,KAAK;AAC1C,gBAAM,gBAA4B;AAAA,YAChC;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AACA,cAAI,eAAe;AACjB,uBAAuB,wBAAI,SAAS,EAAE,SAASA,GAAE,4BAA4B,GAAG,MAAM,SAAS,UAAU,UAAU,CAAC;AAAA,UACtH;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,UAAsB,wBAAI,OAAO,EAAE,WAAW,qBAAqB,UAAUA,GAAE,4BAA4B,EAAE,CAAC;AAAA,QACtH,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,gBAAM,iBAAiB,iBAAiB,IAAI,SAAS,IAAI;AACzD,qBAAuB,wBAAI,OAAO,EAAE,WAAW,iCAAiC,cAA0B;AAAA,YACxG;AAAA,YACA;AAAA,cACE,UAAU;AAAA,cACV,SAAS,kBAAkB;AAAA,cAC3B,iBAAiB,CAAC,QAAQ;AACxB,oCAAoB;AAAA,kBAClB,GAAG;AAAA,kBACH,CAAC,IAAI,SAAS,IAAI,GAAG;AAAA,gBACvB,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,QACL;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAACA,IAAG,MAAM,kBAAkB,mBAAmB;AAAA,EACjD;AACF;AAIA,IAAI,qBAAqB,MAAM;AA9Q/B;AA+QE,QAAM,EAAE,OAAO,WAAW,SAAS,MAAM,IAAI,SAAS;AACtD,QAAM;AAAA,IACJ,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI;AAAA,IACF;AAAA,MACE,WAAW;AAAA,MACX,QAAO,oCAAO,yBAAP,mBAA6B,IAAI,CAAC,MAAM,EAAE;AAAA,IACnD;AAAA,IACA;AAAA,MACE,SAAS,CAAC,CAAC;AAAA,IACb;AAAA,EACF;AACA,QAAM,QAAQ,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,oBAAoB,CAAC;AAC9D,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,MAAI,yBAAyB;AAC3B,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAE,KAAK,iBAAiB,EAAE,UAAU,aAAyB,oBAAAA,KAAK,mBAAmB,EAAE,OAAO,iBAAiB,CAAC,EAAE,CAAC;AAC1I;", "names": ["import_jsx_runtime", "t", "_a", "jsx2"]}