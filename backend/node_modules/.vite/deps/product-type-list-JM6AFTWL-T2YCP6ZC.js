import {
  useDeleteProductTypeAction
} from "./chunk-AAK5OCDV.js";
import {
  useProductTypeTableColumns
} from "./chunk-N55VJRL3.js";
import {
  useProductTypeTableQuery
} from "./chunk-ITWRYKT3.js";
import "./chunk-XYN45SN5.js";
import "./chunk-XFUFIHVF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-32T72GVU.js";
import {
  useProductTypeTableFilters
} from "./chunk-P5T2IZP5.js";
import "./chunk-IONS3C54.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useProductTypes
} from "./chunk-B7WS6CWS.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Text,
  Trash,
  createColumnHelper
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-type-list-JM6AFTWL.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var ProductTypeRowActions = ({
  productType
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTypeAction(
    productType.id,
    productType.value
  );
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.edit"),
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              to: `/settings/product-types/${productType.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              label: t("actions.delete"),
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var PAGE_SIZE = 20;
var ProductTypeListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTypeTableQuery({
    pageSize: PAGE_SIZE
  });
  const { product_types, count, isLoading, isError, error } = useProductTypes(
    searchParams,
    {
      placeholderData: keepPreviousData
    }
  );
  const filters = useProductTypeTableFilters();
  const columns = useColumns();
  const { table } = useDataTable({
    columns,
    data: product_types,
    count,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsxs)("div", { children: [
        (0, import_jsx_runtime2.jsx)(Heading, { children: t("productTypes.domain") }),
        (0, import_jsx_runtime2.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("productTypes.subtitle") })
      ] }),
      (0, import_jsx_runtime2.jsx)(Button, { size: "small", variant: "secondary", asChild: true, children: (0, import_jsx_runtime2.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        filters,
        isLoading,
        columns,
        pageSize: PAGE_SIZE,
        count,
        orderBy: [
          { key: "value", label: t("fields.value") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        navigateTo: ({ original }) => original.id,
        queryObject: raw,
        pagination: true,
        search: true
      }
    )
  ] });
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTypeTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(ProductTypeRowActions, { productType: row.original });
        }
      })
    ],
    [base]
  );
};
var ProductTypeList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime3.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_type.list.after"),
        before: getWidgets("product_type.list.before")
      },
      children: (0, import_jsx_runtime3.jsx)(ProductTypeListTable, {})
    }
  );
};
export {
  ProductTypeList as Component
};
//# sourceMappingURL=product-type-list-JM6AFTWL-T2YCP6ZC.js.map
