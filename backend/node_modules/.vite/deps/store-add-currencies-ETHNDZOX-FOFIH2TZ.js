import {
  useCurrenciesTableColumns,
  useCurrenciesTableQuery
} from "./chunk-52CDFLXQ.js";
import "./chunk-XFUFIHVF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import "./chunk-32T72GVU.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  arrayType,
  booleanType,
  objectType,
  recordType,
  stringType
} from "./chunk-4XXECALA.js";
import "./chunk-XXSIULXV.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  useForm
} from "./chunk-IL7M46GI.js";
import {
  useCurrencies
} from "./chunk-43FR2ATH.js";
import {
  useStore,
  useUpdateStore
} from "./chunk-YXXDSYQ5.js";
import {
  pricePreferencesQueryKeys,
  usePricePreferences
} from "./chunk-6TPPQSEA.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Checkbox,
  Hint,
  Switch,
  Tooltip,
  createColumnHelper,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/store-add-currencies-ETHNDZOX.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var AddCurrenciesSchema = objectType({
  currencies: arrayType(stringType()).min(1),
  pricePreferences: recordType(booleanType())
});
var PAGE_SIZE = 50;
var PREFIX = "ac";
var AddCurrenciesForm = ({
  store,
  pricePreferences
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const { raw, searchParams } = useCurrenciesTableQuery({
    pageSize: 50,
    prefix: PREFIX
  });
  const {
    currencies,
    count,
    isPending: isLoading,
    isError,
    error
  } = useCurrencies(searchParams, {
    placeholderData: keepPreviousData
  });
  const form = useForm({
    defaultValues: {
      currencies: [],
      pricePreferences: pricePreferences == null ? void 0 : pricePreferences.reduce(
        (acc, curr) => {
          if (curr.value) {
            acc[curr.value] = curr.is_tax_inclusive;
          }
          return acc;
        },
        {}
      )
    },
    resolver: t(AddCurrenciesSchema)
  });
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { setValue, watch } = form;
  const pricePreferenceValues = watch("pricePreferences");
  const updater = (fn) => {
    const updated = typeof fn === "function" ? fn(rowSelection) : fn;
    const ids = Object.keys(updated);
    setValue("currencies", ids, {
      shouldDirty: true,
      shouldTouch: true
    });
    setRowSelection(updated);
  };
  const preSelectedRows = ((_a = store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)) ?? [];
  const setPricePreferences = (0, import_react.useCallback)(
    (values) => {
      setValue("pricePreferences", values);
    },
    [setValue]
  );
  const columns = useColumns(pricePreferenceValues, setPricePreferences);
  const { table } = useDataTable({
    data: currencies ?? [],
    columns,
    count,
    getRowId: (row) => row.code,
    enableRowSelection: (row) => !preSelectedRows.includes(row.original.code),
    enablePagination: true,
    pageSize: PAGE_SIZE,
    prefix: PREFIX,
    rowSelection: {
      state: rowSelection,
      updater
    }
  });
  const { mutateAsync, isPending } = useUpdateStore(store.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    var _a2, _b;
    const currencies2 = Array.from(
      /* @__PURE__ */ new Set([...data.currencies, ...preSelectedRows])
    );
    let defaultCurrency = (_b = (_a2 = store.supported_currencies) == null ? void 0 : _a2.find(
      (c) => c.is_default
    )) == null ? void 0 : _b.currency_code;
    if (!currencies2.includes(defaultCurrency ?? "")) {
      defaultCurrency = currencies2 == null ? void 0 : currencies2[0];
    }
    await mutateAsync(
      {
        supported_currencies: currencies2.map((c) => ({
          currency_code: c,
          is_default: c === defaultCurrency,
          is_tax_inclusive: data.pricePreferences[c]
        }))
      },
      {
        onSuccess: () => {
          toast.success(t2("store.toast.currenciesUpdated"));
          queryClient.invalidateQueries({
            queryKey: pricePreferencesQueryKeys.all
          });
          handleSuccess();
        },
        onError: (error2) => {
          toast.error(error2.message);
        }
      }
    );
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 items-center justify-between", children: (0, import_jsx_runtime.jsx)("div", { className: "flex items-center", children: form.formState.errors.currencies && (0, import_jsx_runtime.jsx)(Hint, { variant: "error", children: form.formState.errors.currencies.message }) }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)(
          _DataTable,
          {
            table,
            pageSize: PAGE_SIZE,
            count,
            columns,
            layout: "fill",
            pagination: true,
            search: "autofocus",
            prefix: PREFIX,
            orderBy: [
              { key: "name", label: t2("fields.name") },
              { key: "code", label: t2("fields.code") }
            ],
            isLoading,
            queryObject: raw
          }
        ) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var columnHelper = createColumnHelper();
var useColumns = (pricePreferences, setPricePreferences) => {
  const { t: t2 } = useTranslation();
  const base = useCurrenciesTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          const isPreSelected = !row.getCanSelect();
          const isSelected = row.getIsSelected() || isPreSelected;
          const Component = (0, import_jsx_runtime.jsx)(
            Checkbox,
            {
              checked: isSelected,
              disabled: isPreSelected,
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
          if (isPreSelected) {
            return (0, import_jsx_runtime.jsx)(Tooltip, { content: t2("store.currencyAlreadyAdded"), side: "right", children: Component });
          }
          return Component;
        }
      }),
      ...base,
      columnHelper.display({
        id: "select",
        header: () => (0, import_jsx_runtime.jsx)("div", { className: "whitespace-nowrap", children: t2("fields.taxInclusivePricing") }),
        cell: ({ row }) => {
          const isPreSelected = !row.getCanSelect();
          const isTaxInclusive = pricePreferences[row.original.code];
          return (0, import_jsx_runtime.jsx)("div", { className: "flex items-center justify-end", children: (0, import_jsx_runtime.jsx)(
            Switch,
            {
              disabled: isPreSelected,
              checked: isTaxInclusive ?? false,
              onCheckedChange: (val) => {
                setPricePreferences({
                  ...pricePreferences,
                  [row.original.code]: val
                });
              }
            }
          ) });
        }
      })
    ],
    [t2, base, pricePreferences, setPricePreferences]
  );
};
var StoreAddCurrencies = () => {
  var _a;
  const { store, isPending, isError, error } = useStore();
  const {
    price_preferences: pricePreferences,
    isPending: isPricePreferencesPending,
    isError: isPricePreferencesError,
    error: pricePreferencesError
  } = usePricePreferences(
    {
      attribute: "currency_code",
      value: (_a = store == null ? void 0 : store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code)
    },
    {
      enabled: !!store
    }
  );
  const ready = !!store && !isPending && !!pricePreferences && !isPricePreferencesPending;
  if (isError) {
    throw error;
  }
  if (isPricePreferencesError) {
    throw pricePreferencesError;
  }
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: ready && (0, import_jsx_runtime2.jsx)(AddCurrenciesForm, { store, pricePreferences }) });
};
export {
  StoreAddCurrencies as Component
};
//# sourceMappingURL=store-add-currencies-ETHNDZOX-FOFIH2TZ.js.map
