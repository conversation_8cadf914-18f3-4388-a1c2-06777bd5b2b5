import {
  DeprecatedPercentageInput
} from "./chunk-W4P4VL57.js";
import {
  AddCampaignPromotionFields
} from "./chunk-N7P7IQZN.js";
import {
  RulesFormField
} from "./chunk-LU252EQZ.js";
import {
  CreateCampaignSchema,
  DEFAULT_CAMPAIGN_VALUES
} from "./chunk-N737W24L.js";
import "./chunk-TAVTGMIK.js";
import "./chunk-HP4EYY5H.js";
import {
  getCurrencySymbol
} from "./chunk-H3DTEG3J.js";
import "./chunk-M3SHND6T.js";
import "./chunk-CME5W7KH.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import "./chunk-RQF55WOK.js";
import "./chunk-5QX4V4M4.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-NV2N3EWM.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-IL7M46GI.js";
import {
  useCampaigns,
  useCreatePromotion
} from "./chunk-CN6R4DBW.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Alert,
  Badge,
  Button,
  CurrencyInput2 as CurrencyInput,
  Divider,
  Heading,
  Input,
  ProgressTabs,
  RadioGroup,
  Text,
  clx,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-create-7LABGQBH.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var RuleSchema = z.array(
  z.object({
    id: z.string().optional(),
    attribute: z.string().min(1, { message: "Required field" }),
    operator: z.string().min(1, { message: "Required field" }),
    values: z.union([
      z.number().min(1, { message: "Required field" }),
      z.string().min(1, { message: "Required field" }),
      z.array(z.string()).min(1, { message: "Required field" })
    ]),
    required: z.boolean().optional(),
    disguised: z.boolean().optional(),
    field_type: z.string().optional()
  })
);
var CreatePromotionSchema = z.object({
  template_id: z.string().optional(),
  campaign_id: z.string().optional(),
  campaign_choice: z.enum(["none", "existing", "new"]).optional(),
  is_automatic: z.string().toLowerCase(),
  code: z.string().min(1),
  type: z.enum(["buyget", "standard"]),
  status: z.enum(["draft", "active", "inactive"]),
  rules: RuleSchema,
  application_method: z.object({
    allocation: z.enum(["each", "across"]),
    value: z.number().min(0),
    currency_code: z.string().optional(),
    max_quantity: z.number().optional().nullable(),
    target_rules: RuleSchema,
    buy_rules: RuleSchema,
    type: z.enum(["fixed", "percentage"]),
    target_type: z.enum(["order", "shipping_methods", "items"])
  }),
  campaign: CreateCampaignSchema.optional()
}).refine(
  (data) => {
    if (data.application_method.allocation === "across") {
      return true;
    }
    return data.application_method.allocation === "each" && typeof data.application_method.max_quantity === "number";
  },
  {
    path: ["application_method.max_quantity"],
    message: `required field`
  }
);
var commonHiddenFields = [
  "type",
  "application_method.type",
  "application_method.allocation"
];
var templates = [
  {
    id: "amount_off_products",
    type: "standard",
    title: "Amount off products",
    description: "Discount specific products or collection of products",
    hiddenFields: [...commonHiddenFields],
    defaults: {
      is_automatic: "false",
      type: "standard",
      application_method: {
        allocation: "each",
        target_type: "items",
        type: "fixed"
      }
    }
  },
  {
    id: "amount_off_order",
    type: "standard",
    title: "Amount off order",
    description: "Discounts the total order amount",
    hiddenFields: [...commonHiddenFields],
    defaults: {
      is_automatic: "false",
      type: "standard",
      application_method: {
        allocation: "across",
        target_type: "order",
        type: "fixed"
      }
    }
  },
  {
    id: "percentage_off_product",
    type: "standard",
    title: "Percentage off product",
    description: "Discounts a percentage off selected products",
    hiddenFields: [...commonHiddenFields],
    defaults: {
      is_automatic: "false",
      type: "standard",
      application_method: {
        allocation: "each",
        target_type: "items",
        type: "percentage"
      }
    }
  },
  {
    id: "percentage_off_order",
    type: "standard",
    title: "Percentage off order",
    description: "Discounts a percentage of the total order amount",
    hiddenFields: [...commonHiddenFields],
    defaults: {
      is_automatic: "false",
      type: "standard",
      application_method: {
        allocation: "across",
        target_type: "order",
        type: "percentage"
      }
    }
  },
  {
    id: "buy_get",
    type: "buy_get",
    title: "Buy X Get Y",
    description: "Buy X product(s), get Y product(s)",
    hiddenFields: [...commonHiddenFields, "application_method.value"],
    defaults: {
      is_automatic: "false",
      type: "buyget",
      application_method: {
        type: "percentage",
        value: 100,
        apply_to_quantity: 1,
        max_quantity: 1
      }
    }
  }
];
var defaultValues = {
  campaign_id: void 0,
  template_id: templates[0].id,
  campaign_choice: "none",
  is_automatic: "false",
  code: "",
  type: "standard",
  status: "draft",
  rules: [],
  application_method: {
    allocation: "each",
    type: "fixed",
    target_type: "items",
    max_quantity: 1,
    target_rules: [],
    buy_rules: []
  },
  campaign: void 0
};
var CreatePromotionForm = () => {
  var _a, _b, _c, _d;
  const [tab, setTab] = (0, import_react.useState)(
    "type"
    /* TYPE */
  );
  const [tabState, setTabState] = (0, import_react.useState)({
    [
      "type"
      /* TYPE */
    ]: "in-progress",
    [
      "promotion"
      /* PROMOTION */
    ]: "not-started",
    [
      "campaign"
      /* CAMPAIGN */
    ]: "not-started"
  });
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues,
    resolver: t(CreatePromotionSchema)
  });
  const { setValue, reset, getValues } = form;
  const { mutateAsync: createPromotion } = useCreatePromotion();
  const handleSubmit = form.handleSubmit(
    async (data) => {
      const {
        campaign_choice: _campaignChoice,
        is_automatic,
        template_id: _templateId,
        application_method,
        rules,
        ...promotionData
      } = data;
      const {
        target_rules: targetRulesData = [],
        buy_rules: buyRulesData = [],
        ...applicationMethodData
      } = application_method;
      const disguisedRules = [
        ...targetRulesData.filter((r) => !!r.disguised),
        ...buyRulesData.filter((r) => !!r.disguised),
        ...rules.filter((r) => !!r.disguised)
      ];
      const applicationMethodRuleData = {};
      for (const rule of disguisedRules) {
        applicationMethodRuleData[rule.attribute] = rule.field_type === "number" ? parseInt(rule.values) : rule.values;
      }
      const buildRulesData = (rules2) => {
        return rules2.filter((r) => !r.disguised).map((rule) => ({
          operator: rule.operator,
          attribute: rule.attribute,
          values: rule.values
        }));
      };
      createPromotion(
        {
          ...promotionData,
          rules: buildRulesData(rules),
          application_method: {
            ...applicationMethodData,
            ...applicationMethodRuleData,
            target_rules: buildRulesData(targetRulesData),
            buy_rules: buildRulesData(buyRulesData)
          },
          is_automatic: is_automatic === "true"
        },
        {
          onSuccess: ({ promotion }) => {
            toast.success(
              t2("promotions.toasts.promotionCreateSuccess", {
                code: promotion.code
              })
            );
            handleSuccess(`/promotions/${promotion.id}`);
          },
          onError: (e) => {
            toast.error(e.message);
          }
        }
      );
    },
    async (error) => {
      const { campaign: _campaign, ...rest } = error || {};
      const errorInPromotionTab = !!Object.keys(rest || {}).length;
      if (errorInPromotionTab) {
        toast.error(t2("promotions.errors.promotionTabError"));
      }
    }
  );
  const handleTabChange = async (tab2) => {
    switch (tab2) {
      case "type":
        setTabState((prev) => ({
          ...prev,
          [
            "type"
            /* TYPE */
          ]: "in-progress"
        }));
        setTab(tab2);
        break;
      case "promotion":
        setTabState((prev) => ({
          ...prev,
          [
            "type"
            /* TYPE */
          ]: "completed",
          [
            "promotion"
            /* PROMOTION */
          ]: "in-progress"
        }));
        setTab(tab2);
        break;
      case "campaign": {
        const valid = await form.trigger();
        if (!valid) {
          setTabState({
            [
              "type"
              /* TYPE */
            ]: "completed",
            [
              "promotion"
              /* PROMOTION */
            ]: "in-progress",
            [
              "campaign"
              /* CAMPAIGN */
            ]: "not-started"
          });
          setTab(
            "promotion"
            /* PROMOTION */
          );
          break;
        }
        setTabState((prev) => ({
          ...prev,
          [
            "promotion"
            /* PROMOTION */
          ]: "completed",
          [
            "campaign"
            /* CAMPAIGN */
          ]: "in-progress"
        }));
        setTab(tab2);
        break;
      }
    }
  };
  const handleContinue = async () => {
    switch (tab) {
      case "type":
        handleTabChange(
          "promotion"
          /* PROMOTION */
        );
        break;
      case "promotion": {
        const valid = await form.trigger();
        if (valid) {
          handleTabChange(
            "campaign"
            /* CAMPAIGN */
          );
        }
        break;
      }
      case "campaign":
        break;
    }
  };
  const watchTemplateId = useWatch({
    control: form.control,
    name: "template_id"
  });
  const currentTemplate = (0, import_react.useMemo)(() => {
    const currentTemplate2 = templates.find(
      (template) => template.id === watchTemplateId
    );
    if (!currentTemplate2) {
      return;
    }
    reset({ ...defaultValues, template_id: watchTemplateId });
    for (const [key, value] of Object.entries(currentTemplate2.defaults)) {
      if (typeof value === "object") {
        for (const [subKey, subValue] of Object.entries(value)) {
          setValue(`application_method.${subKey}`, subValue);
        }
      } else {
        setValue(key, value);
      }
    }
    return currentTemplate2;
  }, [watchTemplateId, setValue, reset]);
  const watchValueType = useWatch({
    control: form.control,
    name: "application_method.type"
  });
  const isFixedValueType = watchValueType === "fixed";
  const watchAllocation = useWatch({
    control: form.control,
    name: "application_method.allocation"
  });
  (0, import_react.useEffect)(() => {
    if (watchAllocation === "across") {
      setValue("application_method.max_quantity", null);
    }
  }, [watchAllocation, setValue]);
  const watchType = useWatch({
    control: form.control,
    name: "type"
  });
  const isTypeStandard = watchType === "standard";
  const targetType = useWatch({
    control: form.control,
    name: "application_method.target_type"
  });
  const isTargetTypeOrder = targetType === "order";
  const formData = form.getValues();
  let campaignQuery = {};
  if (isFixedValueType && formData.application_method.currency_code) {
    campaignQuery = {
      budget: { currency_code: formData.application_method.currency_code }
    };
  }
  const { campaigns } = useCampaigns(campaignQuery);
  const watchCampaignChoice = useWatch({
    control: form.control,
    name: "campaign_choice"
  });
  (0, import_react.useEffect)(() => {
    var _a2, _b2;
    const formData2 = getValues();
    if (watchCampaignChoice !== "existing") {
      setValue("campaign_id", void 0);
    }
    if (watchCampaignChoice !== "new") {
      setValue("campaign", void 0);
    }
    if (watchCampaignChoice === "new") {
      if (!formData2.campaign || !((_b2 = (_a2 = formData2.campaign) == null ? void 0 : _a2.budget) == null ? void 0 : _b2.type)) {
        setValue("campaign", {
          ...DEFAULT_CAMPAIGN_VALUES,
          budget: {
            ...DEFAULT_CAMPAIGN_VALUES.budget,
            currency_code: formData2.application_method.currency_code
          }
        });
      }
    }
  }, [watchCampaignChoice, getValues, setValue]);
  const watchRules = useWatch({
    control: form.control,
    name: "rules"
  });
  const watchCurrencyRule = watchRules.find(
    (rule) => rule.attribute === "currency_code"
  );
  if (watchCurrencyRule) {
    const formData2 = form.getValues();
    const currencyCode = formData2.application_method.currency_code;
    const ruleValue = watchCurrencyRule.values;
    if (!Array.isArray(ruleValue) && currencyCode !== ruleValue) {
      form.setValue("application_method.currency_code", ruleValue);
    }
  }
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(KeyboundForm, { className: "flex h-full flex-col", onSubmit: handleSubmit, children: [
    (0, import_jsx_runtime.jsxs)(
      ProgressTabs,
      {
        value: tab,
        onValueChange: (tab2) => handleTabChange(tab2),
        className: "flex h-full flex-col overflow-hidden",
        children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, { children: (0, import_jsx_runtime.jsx)("div", { className: "flex w-full items-center justify-between gap-x-4", children: (0, import_jsx_runtime.jsx)("div", { className: "-my-2 w-full max-w-[600px] border-l", children: (0, import_jsx_runtime.jsxs)(ProgressTabs.List, { className: "grid w-full grid-cols-3", children: [
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Trigger,
              {
                className: "w-full",
                value: "type",
                status: tabState[
                  "type"
                  /* TYPE */
                ],
                children: t2("promotions.tabs.template")
              }
            ),
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Trigger,
              {
                className: "w-full",
                value: "promotion",
                status: tabState[
                  "promotion"
                  /* PROMOTION */
                ],
                children: t2("promotions.tabs.details")
              }
            ),
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Trigger,
              {
                className: "w-full",
                value: "campaign",
                status: tabState[
                  "campaign"
                  /* CAMPAIGN */
                ],
                children: t2("promotions.tabs.campaign")
              }
            )
          ] }) }) }) }),
          (0, import_jsx_runtime.jsxs)(RouteFocusModal.Body, { className: "size-full overflow-hidden", children: [
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Content,
              {
                value: "type",
                className: "size-full overflow-y-auto",
                children: (0, import_jsx_runtime.jsx)("div", { className: "flex size-full flex-col items-center", children: (0, import_jsx_runtime.jsx)("div", { className: "w-full max-w-[720px] py-16", children: (0, import_jsx_runtime.jsx)(
                  Form.Field,
                  {
                    control: form.control,
                    name: "template_id",
                    render: ({ field }) => {
                      return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.type") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          RadioGroup,
                          {
                            className: "flex-col gap-y-3",
                            ...field,
                            onValueChange: field.onChange,
                            children: templates.map((template) => {
                              return (0, import_jsx_runtime.jsx)(
                                RadioGroup.ChoiceBox,
                                {
                                  value: template.id,
                                  label: template.title,
                                  description: template.description
                                },
                                template.id
                              );
                            })
                          },
                          "template_id"
                        ) }),
                        (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                      ] });
                    }
                  }
                ) }) })
              }
            ),
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Content,
              {
                value: "promotion",
                className: "size-full overflow-y-auto",
                children: (0, import_jsx_runtime.jsx)("div", { className: "flex size-full flex-col items-center", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 py-16", children: [
                  (0, import_jsx_runtime.jsxs)(Heading, { level: "h1", className: "text-fg-base", children: [
                    t2(`promotions.sections.details`),
                    (currentTemplate == null ? void 0 : currentTemplate.title) && (0, import_jsx_runtime.jsx)(
                      Badge,
                      {
                        className: "ml-2 align-middle",
                        color: "grey",
                        size: "2xsmall",
                        rounded: "full",
                        children: currentTemplate == null ? void 0 : currentTemplate.title
                      }
                    )
                  ] }),
                  form.formState.errors.root && (0, import_jsx_runtime.jsx)(
                    Alert,
                    {
                      variant: "error",
                      dismissible: false,
                      className: "text-balance",
                      children: form.formState.errors.root.message
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "is_automatic",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.method.label") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                            RadioGroup,
                            {
                              className: "flex gap-y-3",
                              ...field,
                              value: field.value,
                              onValueChange: field.onChange,
                              children: [
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "false",
                                    label: t2("promotions.form.method.code.title"),
                                    description: t2(
                                      "promotions.form.method.code.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                ),
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "true",
                                    label: t2(
                                      "promotions.form.method.automatic.title"
                                    ),
                                    description: t2(
                                      "promotions.form.method.automatic.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                )
                              ]
                            }
                          ) }),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                        ] });
                      }
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "status",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.status.label") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                            RadioGroup,
                            {
                              className: "flex gap-y-3",
                              ...field,
                              value: field.value,
                              onValueChange: field.onChange,
                              children: [
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "draft",
                                    label: t2("promotions.form.status.draft.title"),
                                    description: t2(
                                      "promotions.form.status.draft.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                ),
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "active",
                                    label: t2("promotions.form.status.active.title"),
                                    description: t2(
                                      "promotions.form.status.active.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                )
                              ]
                            }
                          ) }),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                        ] });
                      }
                    }
                  ),
                  (0, import_jsx_runtime.jsx)("div", { className: "flex gap-y-4", children: (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "code",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.code.title") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field, placeholder: "SUMMER15" }) }),
                          (0, import_jsx_runtime.jsx)(
                            Text,
                            {
                              size: "small",
                              leading: "compact",
                              className: "text-ui-fg-subtle",
                              children: (0, import_jsx_runtime.jsx)(
                                Trans,
                                {
                                  t: t2,
                                  i18nKey: "promotions.form.code.description",
                                  components: [(0, import_jsx_runtime.jsx)("br", {}, "break")]
                                }
                              )
                            }
                          )
                        ] });
                      }
                    }
                  ) }),
                  !((_a = currentTemplate == null ? void 0 : currentTemplate.hiddenFields) == null ? void 0 : _a.includes("type")) && (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "type",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.type") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                            RadioGroup,
                            {
                              className: "flex gap-y-3",
                              ...field,
                              onValueChange: field.onChange,
                              children: [
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "standard",
                                    label: t2(
                                      "promotions.form.type.standard.title"
                                    ),
                                    description: t2(
                                      "promotions.form.type.standard.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                ),
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "buyget",
                                    label: t2("promotions.form.type.buyget.title"),
                                    description: t2(
                                      "promotions.form.type.buyget.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                )
                              ]
                            }
                          ) }),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                        ] });
                      }
                    }
                  ),
                  (0, import_jsx_runtime.jsx)(Divider, {}),
                  (0, import_jsx_runtime.jsx)(RulesFormField, { form, ruleType: "rules" }),
                  (0, import_jsx_runtime.jsx)(Divider, {}),
                  !((_b = currentTemplate == null ? void 0 : currentTemplate.hiddenFields) == null ? void 0 : _b.includes(
                    "application_method.type"
                  )) && (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "application_method.type",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.value_type") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                            RadioGroup,
                            {
                              className: "flex gap-y-3",
                              ...field,
                              onValueChange: field.onChange,
                              children: [
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "fixed",
                                    label: t2(
                                      "promotions.form.value_type.fixed.title"
                                    ),
                                    description: t2(
                                      "promotions.form.value_type.fixed.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                ),
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "percentage",
                                    label: t2(
                                      "promotions.form.value_type.percentage.title"
                                    ),
                                    description: t2(
                                      "promotions.form.value_type.percentage.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                )
                              ]
                            }
                          ) }),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                        ] });
                      }
                    }
                  ),
                  (0, import_jsx_runtime.jsxs)("div", { className: "flex gap-x-2 gap-y-4", children: [
                    !((_c = currentTemplate == null ? void 0 : currentTemplate.hiddenFields) == null ? void 0 : _c.includes(
                      "application_method.value"
                    )) && (0, import_jsx_runtime.jsx)(
                      Form.Field,
                      {
                        control: form.control,
                        name: "application_method.value",
                        render: ({ field: { onChange, value, ...field } }) => {
                          const currencyCode = form.getValues().application_method.currency_code;
                          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
                            (0, import_jsx_runtime.jsx)(
                              Form.Label,
                              {
                                tooltip: currencyCode || !isFixedValueType ? void 0 : t2("promotions.fields.amount.tooltip"),
                                children: t2("promotions.form.value.title")
                              }
                            ),
                            (0, import_jsx_runtime.jsx)(Form.Control, { children: isFixedValueType ? (0, import_jsx_runtime.jsx)(
                              CurrencyInput,
                              {
                                ...field,
                                min: 0,
                                onValueChange: (value2) => {
                                  onChange(value2 ? parseInt(value2) : "");
                                },
                                code: currencyCode || "USD",
                                symbol: currencyCode ? getCurrencySymbol(currencyCode) : "$",
                                value,
                                disabled: !currencyCode
                              }
                            ) : (0, import_jsx_runtime.jsx)(
                              DeprecatedPercentageInput,
                              {
                                className: "text-right",
                                min: 0,
                                max: 100,
                                ...field,
                                value,
                                onChange: (e) => {
                                  onChange(
                                    e.target.value === "" ? null : parseInt(e.target.value)
                                  );
                                }
                              },
                              "amount"
                            ) }),
                            (0, import_jsx_runtime.jsx)(
                              Text,
                              {
                                size: "small",
                                leading: "compact",
                                className: "text-ui-fg-subtle",
                                children: (0, import_jsx_runtime.jsx)(
                                  Trans,
                                  {
                                    t: t2,
                                    i18nKey: isFixedValueType ? "promotions.form.value_type.fixed.description" : "promotions.form.value_type.percentage.description",
                                    components: [(0, import_jsx_runtime.jsx)("br", {}, "break")]
                                  }
                                )
                              }
                            ),
                            (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                          ] });
                        }
                      }
                    ),
                    isTypeStandard && watchAllocation === "each" && (0, import_jsx_runtime.jsx)(
                      Form.Field,
                      {
                        control: form.control,
                        name: "application_method.max_quantity",
                        render: ({ field }) => {
                          return (0, import_jsx_runtime.jsxs)(Form.Item, { className: "basis-1/2", children: [
                            (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.max_quantity.title") }),
                            (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                              Input,
                              {
                                ...form.register(
                                  "application_method.max_quantity",
                                  { valueAsNumber: true }
                                ),
                                type: "number",
                                min: 1,
                                placeholder: "3"
                              }
                            ) }),
                            (0, import_jsx_runtime.jsx)(
                              Text,
                              {
                                size: "small",
                                leading: "compact",
                                className: "text-ui-fg-subtle",
                                children: (0, import_jsx_runtime.jsx)(
                                  Trans,
                                  {
                                    t: t2,
                                    i18nKey: "promotions.form.max_quantity.description",
                                    components: [(0, import_jsx_runtime.jsx)("br", {}, "break")]
                                  }
                                )
                              }
                            )
                          ] });
                        }
                      }
                    )
                  ] }),
                  isTypeStandard && !((_d = currentTemplate == null ? void 0 : currentTemplate.hiddenFields) == null ? void 0 : _d.includes(
                    "application_method.allocation"
                  )) && (0, import_jsx_runtime.jsx)(
                    Form.Field,
                    {
                      control: form.control,
                      name: "application_method.allocation",
                      render: ({ field }) => {
                        return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                          (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.allocation") }),
                          (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                            RadioGroup,
                            {
                              className: "flex gap-y-3",
                              ...field,
                              onValueChange: field.onChange,
                              children: [
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "each",
                                    label: t2(
                                      "promotions.form.allocation.each.title"
                                    ),
                                    description: t2(
                                      "promotions.form.allocation.each.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                ),
                                (0, import_jsx_runtime.jsx)(
                                  RadioGroup.ChoiceBox,
                                  {
                                    value: "across",
                                    label: t2(
                                      "promotions.form.allocation.across.title"
                                    ),
                                    description: t2(
                                      "promotions.form.allocation.across.description"
                                    ),
                                    className: clx("basis-1/2")
                                  }
                                )
                              ]
                            }
                          ) }),
                          (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                        ] });
                      }
                    }
                  ),
                  !isTypeStandard && (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: (0, import_jsx_runtime.jsx)(
                    RulesFormField,
                    {
                      form,
                      ruleType: "buy-rules",
                      scope: "application_method.buy_rules"
                    }
                  ) }),
                  !isTargetTypeOrder && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
                    (0, import_jsx_runtime.jsx)(Divider, {}),
                    (0, import_jsx_runtime.jsx)(
                      RulesFormField,
                      {
                        form,
                        ruleType: "target-rules",
                        scope: "application_method.target_rules"
                      }
                    )
                  ] })
                ] }) })
              }
            ),
            (0, import_jsx_runtime.jsx)(
              ProgressTabs.Content,
              {
                value: "campaign",
                className: "size-full overflow-auto",
                children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-col items-center", children: (0, import_jsx_runtime.jsx)("div", { className: "flex w-full max-w-[720px] flex-col gap-y-8 py-16", children: (0, import_jsx_runtime.jsx)(
                  AddCampaignPromotionFields,
                  {
                    form,
                    campaigns: campaigns || []
                  }
                ) }) })
              }
            )
          ] })
        ]
      }
    ),
    (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
      (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
      tab === "campaign" ? (0, import_jsx_runtime.jsx)(
        Button,
        {
          type: "submit",
          size: "small",
          isLoading: false,
          children: t2("actions.save")
        },
        "save-btn"
      ) : (0, import_jsx_runtime.jsx)(
        Button,
        {
          type: "button",
          onClick: handleContinue,
          size: "small",
          children: t2("actions.continue")
        },
        "continue-btn"
      )
    ] }) })
  ] }) });
};
var PromotionCreate = () => {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreatePromotionForm, {}) });
};
export {
  PromotionCreate as Component
};
//# sourceMappingURL=promotion-create-7LABGQBH-TSQ3HF54.js.map
