{"version": 3, "sources": ["../../@medusajs/dashboard/dist/promotion-add-campaign-4MRPKION.mjs"], "sourcesContent": ["import {\n  AddCampaignPromotionForm\n} from \"./chunk-AYAWUL3C.mjs\";\nimport \"./chunk-TDDYKNA2.mjs\";\nimport \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer\n} from \"./chunk-4TC5YS65.mjs\";\nimport \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCampaigns,\n  usePromotion\n} from \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/promotions/promotion-add-campaign/promotion-add-campaign.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar PromotionAddCampaign = () => {\n  const { id } = useParams();\n  const { t } = useTranslation();\n  const { promotion, isPending, isError, error } = usePromotion(id);\n  let campaignQuery = {};\n  if (promotion?.application_method?.currency_code) {\n    campaignQuery = {\n      budget: {\n        currency_code: promotion?.application_method?.currency_code\n      }\n    };\n  }\n  const {\n    campaigns,\n    isPending: areCampaignsLoading,\n    isError: isCampaignError,\n    error: campaignError\n  } = useCampaigns(campaignQuery);\n  if (isError || isCampaignError) {\n    throw error || campaignError;\n  }\n  return /* @__PURE__ */ jsxs(RouteDrawer, { children: [\n    /* @__PURE__ */ jsx(RouteDrawer.Header, { children: /* @__PURE__ */ jsx(Heading, { children: t(\"promotions.campaign.edit.header\") }) }),\n    !isPending && !areCampaignsLoading && promotion && campaigns && /* @__PURE__ */ jsx(AddCampaignPromotionForm, { promotion, campaigns })\n  ] });\n};\nexport {\n  PromotionAddCampaign as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,yBAA0B;AAC1B,IAAI,uBAAuB,MAAM;AA3BjC;AA4BE,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,WAAW,WAAW,SAAS,MAAM,IAAI,aAAa,EAAE;AAChE,MAAI,gBAAgB,CAAC;AACrB,OAAI,4CAAW,uBAAX,mBAA+B,eAAe;AAChD,oBAAgB;AAAA,MACd,QAAQ;AAAA,QACN,gBAAe,4CAAW,uBAAX,mBAA+B;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,EACT,IAAI,aAAa,aAAa;AAC9B,MAAI,WAAW,iBAAiB;AAC9B,UAAM,SAAS;AAAA,EACjB;AACA,aAAuB,yBAAK,aAAa,EAAE,UAAU;AAAA,QACnC,wBAAI,YAAY,QAAQ,EAAE,cAA0B,wBAAI,SAAS,EAAE,UAAU,EAAE,iCAAiC,EAAE,CAAC,EAAE,CAAC;AAAA,IACtI,CAAC,aAAa,CAAC,uBAAuB,aAAa,iBAA6B,wBAAI,0BAA0B,EAAE,WAAW,UAAU,CAAC;AAAA,EACxI,EAAE,CAAC;AACL;", "names": []}