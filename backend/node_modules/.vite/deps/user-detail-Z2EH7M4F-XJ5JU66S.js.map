{"version": 3, "sources": ["../../@medusajs/dashboard/dist/user-detail-Z2EH7M4F.mjs"], "sourcesContent": ["import {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  useDeleteUser,\n  useUser\n} from \"./chunk-2ZKVRTBW.mjs\";\nimport {\n  productsQueryKeys\n} from \"./chunk-6I62UDJA.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/users/user-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar UserDetailBreadcrumb = (props) => {\n  const { id } = props.params || {};\n  const { user } = useUser(id, void 0, {\n    initialData: props.data,\n    enabled: Boolean(id)\n  });\n  if (!user) {\n    return null;\n  }\n  const name = [user.first_name, user.last_name].filter(Boolean).join(\" \");\n  const display = name || user.email;\n  return /* @__PURE__ */ jsx(\"span\", { children: display });\n};\n\n// src/routes/users/user-detail/loader.ts\nvar userDetailQuery = (id) => ({\n  queryKey: productsQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.user.retrieve(id)\n});\nvar userLoader = async ({ params }) => {\n  const id = params.id;\n  const query = userDetailQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/users/user-detail/user-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/users/user-detail/components/user-general-section/user-general-section.tsx\nimport { PencilSquare, Trash } from \"@medusajs/icons\";\nimport { Container, Heading, Text, toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar UserGeneralSection = ({ user }) => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const prompt = usePrompt();\n  const { mutateAsync } = useDeleteUser(user.id);\n  const name = [user.first_name, user.last_name].filter(Boolean).join(\" \");\n  const handleDeleteUser = async () => {\n    const res = await prompt({\n      title: t(\"general.areYouSure\"),\n      description: t(\"users.deleteUserWarning\", {\n        name: name ?? user.email\n      }),\n      verificationText: name ?? user.email,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(t(\"users.deleteUserSuccess\", { name: user.email }));\n        navigate(\"..\");\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: user.email }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  label: t(\"actions.edit\"),\n                  to: \"edit\",\n                  icon: /* @__PURE__ */ jsx2(PencilSquare, {})\n                }\n              ]\n            },\n            {\n              actions: [\n                {\n                  label: t(\"actions.delete\"),\n                  onClick: handleDeleteUser,\n                  icon: /* @__PURE__ */ jsx2(Trash, {})\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsxs(\"div\", { className: \"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.name\") }),\n      /* @__PURE__ */ jsx2(Text, { size: \"small\", leading: \"compact\", children: name ?? \"-\" })\n    ] })\n  ] });\n};\n\n// src/routes/users/user-detail/user-detail.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar UserDetail = () => {\n  const initialData = useLoaderData();\n  const { id } = useParams();\n  const {\n    user,\n    isPending: isLoading,\n    isError,\n    error\n  } = useUser(id, void 0, {\n    initialData\n  });\n  const { getWidgets } = useExtension();\n  if (isLoading || !user) {\n    return /* @__PURE__ */ jsx3(SingleColumnPageSkeleton, { sections: 1, showJSON: true, showMetadata: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      data: user,\n      showJSON: true,\n      showMetadata: true,\n      widgets: {\n        after: getWidgets(\"user.details.after\"),\n        before: getWidgets(\"user.details.before\")\n      },\n      children: /* @__PURE__ */ jsx3(UserGeneralSection, { user })\n    }\n  );\n};\nexport {\n  UserDetailBreadcrumb as Breadcrumb,\n  UserDetail as Component,\n  userLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,yBAAoB;AAkCpB,IAAAA,sBAAkC;AAoElC,IAAAA,sBAA4B;AArG5B,IAAI,uBAAuB,CAAC,UAAU;AACpC,QAAM,EAAE,GAAG,IAAI,MAAM,UAAU,CAAC;AAChC,QAAM,EAAE,KAAK,IAAI,QAAQ,IAAI,QAAQ;AAAA,IACnC,aAAa,MAAM;AAAA,IACnB,SAAS,QAAQ,EAAE;AAAA,EACrB,CAAC;AACD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,QAAM,OAAO,CAAC,KAAK,YAAY,KAAK,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AACvE,QAAM,UAAU,QAAQ,KAAK;AAC7B,aAAuB,wBAAI,QAAQ,EAAE,UAAU,QAAQ,CAAC;AAC1D;AAGA,IAAI,kBAAkB,CAAC,QAAQ;AAAA,EAC7B,UAAU,kBAAkB,OAAO,EAAE;AAAA,EACrC,SAAS,YAAY,IAAI,MAAM,KAAK,SAAS,EAAE;AACjD;AACA,IAAI,aAAa,OAAO,EAAE,OAAO,MAAM;AACrC,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,gBAAgB,EAAE;AAChC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAWA,IAAI,qBAAqB,CAAC,EAAE,KAAK,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,YAAY,IAAI,cAAc,KAAK,EAAE;AAC7C,QAAM,OAAO,CAAC,KAAK,YAAY,KAAK,SAAS,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AACvE,QAAM,mBAAmB,YAAY;AACnC,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,oBAAoB;AAAA,MAC7B,aAAa,EAAE,2BAA2B;AAAA,QACxC,MAAM,QAAQ,KAAK;AAAA,MACrB,CAAC;AAAA,MACD,kBAAkB,QAAQ,KAAK;AAAA,MAC/B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM,QAAQ,EAAE,2BAA2B,EAAE,MAAM,KAAK,MAAM,CAAC,CAAC;AAChE,iBAAS,IAAI;AAAA,MACf;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,UAAU,KAAK,MAAM,CAAC;AAAA,UACtC,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,cAAc;AAAA,kBACvB,IAAI;AAAA,kBACJ,UAAsB,oBAAAA,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,OAAO,EAAE,gBAAgB;AAAA,kBACzB,SAAS;AAAA,kBACT,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,gBACtC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,0BAAK,OAAO,EAAE,WAAW,6DAA6D,UAAU;AAAA,UAC9F,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAU,EAAE,aAAa,EAAE,CAAC;AAAA,UAC5F,oBAAAA,KAAK,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,UAAU,QAAQ,IAAI,CAAC;AAAA,IACzF,EAAE,CAAC;AAAA,EACL,EAAE,CAAC;AACL;AAIA,IAAI,aAAa,MAAM;AACrB,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF,IAAI,QAAQ,IAAI,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC;AACD,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,MAAM;AACtB,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN,UAAU;AAAA,MACV,cAAc;AAAA,MACd,SAAS;AAAA,QACP,OAAO,WAAW,oBAAoB;AAAA,QACtC,QAAQ,WAAW,qBAAqB;AAAA,MAC1C;AAAA,MACA,cAA0B,oBAAAA,KAAK,oBAAoB,EAAE,KAAK,CAAC;AAAA,IAC7D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}