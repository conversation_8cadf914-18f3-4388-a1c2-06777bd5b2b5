{"version": 3, "sources": ["../../@medusajs/dashboard/dist/sales-channel-create-GUI5GFH3.mjs"], "sourcesContent": ["import \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  useCreateSalesChannel\n} from \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/sales-channels/sales-channel-create/components/create-sales-channel-form/create-sales-channel-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport {\n  Button,\n  Heading,\n  Input,\n  Switch,\n  Text,\n  Textarea,\n  toast\n} from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateSalesChannelSchema = zod.object({\n  name: zod.string().min(1),\n  description: zod.string().min(1),\n  enabled: zod.boolean()\n});\nvar CreateSalesChannelForm = () => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      description: \"\",\n      enabled: true\n    },\n    resolver: zodResolver(CreateSalesChannelSchema)\n  });\n  const { mutateAsync, isPending } = useCreateSalesChannel();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        name: values.name,\n        description: values.description,\n        is_disabled: !values.enabled\n      },\n      {\n        onSuccess: ({ sales_channel }) => {\n          toast.success(t(\"salesChannels.toast.create\"));\n          handleSuccess(`../${sales_channel.id}`);\n        },\n        onError: (error) => toast.error(error.message)\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      onSubmit: handleSubmit,\n      className: \"flex h-full flex-col overflow-hidden\",\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, {}),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex flex-1 flex-col overflow-hidden\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-1 flex-col items-center overflow-y-auto\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16\", children: [\n          /* @__PURE__ */ jsxs(\"div\", { children: [\n            /* @__PURE__ */ jsx(Heading, { className: \"capitalize\", children: t(\"salesChannels.createSalesChannel\") }),\n            /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"salesChannels.createSalesChannelHint\") })\n          ] }),\n          /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n            /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-2\", children: /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"name\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { size: \"small\", ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            ) }),\n            /* @__PURE__ */ jsx(\n              Form.Field,\n              {\n                control: form.control,\n                name: \"description\",\n                render: ({ field }) => {\n                  return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.description\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Textarea, { ...field }) }),\n                    /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                  ] });\n                }\n              }\n            )\n          ] }),\n          /* @__PURE__ */ jsx(\n            Form.Field,\n            {\n              control: form.control,\n              name: \"enabled\",\n              render: ({ field: { value, onChange, ...field } }) => {\n                return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                  /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between\", children: [\n                    /* @__PURE__ */ jsx(Form.Label, { children: t(\"general.enabled\") }),\n                    /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                      Switch,\n                      {\n                        ...field,\n                        checked: value,\n                        onCheckedChange: onChange\n                      }\n                    ) })\n                  ] }),\n                  /* @__PURE__ */ jsx(Form.Hint, { children: t(\"salesChannels.enabledHint\") }),\n                  /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                ] });\n              }\n            }\n          )\n        ] }) }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/sales-channels/sales-channel-create/sales-channel-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar SalesChannelCreate = () => {\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: /* @__PURE__ */ jsx2(CreateSalesChannelForm, {}) });\n};\nexport {\n  SalesChannelCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,yBAA0B;AA+G1B,IAAAA,sBAA4B;AA9G5B,IAAI,2BAA+B,WAAO;AAAA,EACxC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,aAAiB,WAAO,EAAE,IAAI,CAAC;AAAA,EAC/B,SAAa,YAAQ;AACvB,CAAC;AACD,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,UAAU,EAAY,wBAAwB;AAAA,EAChD,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,sBAAsB;AACzD,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,QACb,aAAa,OAAO;AAAA,QACpB,aAAa,CAAC,OAAO;AAAA,MACvB;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,cAAc,MAAM;AAChC,gBAAM,QAAQA,GAAE,4BAA4B,CAAC;AAC7C,wBAAc,MAAM,cAAc,EAAE,EAAE;AAAA,QACxC;AAAA,QACA,SAAS,CAAC,UAAU,MAAM,MAAM,MAAM,OAAO;AAAA,MAC/C;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,CAAC,CAAC;AAAA,YAC9B,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wCAAwC,cAA0B,wBAAI,OAAO,EAAE,WAAW,qDAAqD,cAA0B,yBAAK,OAAO,EAAE,WAAW,yDAAyD,UAAU;AAAA,cAC1S,yBAAK,OAAO,EAAE,UAAU;AAAA,gBACtB,wBAAI,SAAS,EAAE,WAAW,cAAc,UAAUA,GAAE,kCAAkC,EAAE,CAAC;AAAA,gBACzF,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,sCAAsC,EAAE,CAAC;AAAA,UAClI,EAAE,CAAC;AAAA,cACa,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,gBAC1D,wBAAI,OAAO,EAAE,WAAW,oBAAoB,cAA0B;AAAA,cACpF,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,wBAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBACvF,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF,EAAE,CAAC;AAAA,gBACa;AAAA,cACd,KAAK;AAAA,cACL;AAAA,gBACE,SAAS,KAAK;AAAA,gBACd,MAAM;AAAA,gBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,6BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,wBACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,oBAAoB,EAAE,CAAC;AAAA,wBACrD,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,wBAC3E,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,kBAC3C,EAAE,CAAC;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,CAAC;AAAA,cACa;AAAA,YACd,KAAK;AAAA,YACL;AAAA,cACE,SAAS,KAAK;AAAA,cACd,MAAM;AAAA,cACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,2BAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,sBACjC,yBAAK,OAAO,EAAE,WAAW,qCAAqC,UAAU;AAAA,wBACtE,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,wBAClD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,sBAC5D;AAAA,sBACA;AAAA,wBACE,GAAG;AAAA,wBACH,SAAS;AAAA,wBACT,iBAAiB;AAAA,sBACnB;AAAA,oBACF,EAAE,CAAC;AAAA,kBACL,EAAE,CAAC;AAAA,sBACa,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,2BAA2B,EAAE,CAAC;AAAA,sBAC3D,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,gBAC3C,EAAE,CAAC;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,YACO,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,qBAAqB,MAAM;AAC7B,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,cAA0B,oBAAAA,KAAK,wBAAwB,CAAC,CAAC,EAAE,CAAC;AAC7G;", "names": ["import_jsx_runtime", "t", "jsx2"]}