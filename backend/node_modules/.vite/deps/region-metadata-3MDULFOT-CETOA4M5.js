import "./chunk-EGRHWZRV.js";
import {
  MetadataForm
} from "./chunk-54JWGIP4.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import {
  RouteDrawer
} from "./chunk-L2KJ2QJA.js";
import "./chunk-7FOP5RO4.js";
import "./chunk-4XXECALA.js";
import "./chunk-XXSIULXV.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-IL7M46GI.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import {
  useRegion,
  useUpdateRegion
} from "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-metadata-3MDULFOT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var RegionMetadata = () => {
  const { id } = useParams();
  const { region, isPending, isError, error } = useRegion(id);
  const { mutateAsync, isPending: isMutating } = useUpdateRegion(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(RouteDrawer, { children: (0, import_jsx_runtime.jsx)(
    MetadataForm,
    {
      isPending,
      isMutating,
      hook: mutateAsync,
      metadata: region == null ? void 0 : region.metadata
    }
  ) });
};
export {
  RegionMetadata as Component
};
//# sourceMappingURL=region-metadata-3MDULFOT-CETOA4M5.js.map
