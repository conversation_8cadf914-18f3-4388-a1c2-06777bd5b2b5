import {
  l
} from "./chunk-T6Z4MBXP.js";
import "./chunk-GAYUCSOM.js";
import "./chunk-HM7ZXGFD.js";
import "./chunk-EGRHWZRV.js";
import {
  motion
} from "./chunk-7M4ICL3D.js";
import "./chunk-YHTHQMGS.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  ZodIssueCode,
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  instance
} from "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import {
  useResetPasswordForEmailPass,
  useUpdateProviderForEmailPass
} from "./chunk-WNILWPA2.js";
import "./chunk-DVDTANCJ.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useNavigate,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Al<PERSON>,
  Button,
  Heading,
  Input,
  Text,
  clx,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/reset-password-UUL5N3DT.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var LogoBox = ({
  className,
  checked,
  containerTransition = {
    duration: 0.8,
    delay: 0.5,
    ease: [0, 0.71, 0.2, 1.01]
  },
  pathTransition = {
    duration: 0.8,
    delay: 0.6,
    ease: [0.1, 0.8, 0.2, 1.01]
  }
}) => {
  return (0, import_jsx_runtime.jsxs)(
    "div",
    {
      className: clx(
        "size-14 bg-ui-button-neutral shadow-buttons-neutral relative flex items-center justify-center rounded-xl",
        "after:button-neutral-gradient after:inset-0 after:content-['']",
        className
      ),
      children: [
        checked && (0, import_jsx_runtime.jsx)(
          motion.div,
          {
            className: "size-5 absolute -right-[5px] -top-1 flex items-center justify-center rounded-full border-[0.5px] border-[rgba(3,7,18,0.2)] bg-[#3B82F6] bg-gradient-to-b from-white/0 to-white/20 shadow-[0px_1px_2px_0px_rgba(3,7,18,0.12),0px_1px_2px_0px_rgba(255,255,255,0.10)_inset,0px_-1px_5px_0px_rgba(255,255,255,0.10)_inset,0px_0px_0px_0px_rgba(3,7,18,0.06)_inset]",
            initial: { opacity: 0, scale: 0.5 },
            animate: { opacity: 1, scale: 1 },
            transition: containerTransition,
            children: (0, import_jsx_runtime.jsx)(
              "svg",
              {
                xmlns: "http://www.w3.org/2000/svg",
                width: "20",
                height: "20",
                viewBox: "0 0 20 20",
                fill: "none",
                children: (0, import_jsx_runtime.jsx)(
                  motion.path,
                  {
                    d: "M5.8335 10.4167L9.16683 13.75L14.1668 6.25",
                    stroke: "white",
                    strokeWidth: "1.5",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    initial: { pathLength: 0, opacity: 0 },
                    animate: { pathLength: 1, opacity: 1 },
                    transition: pathTransition
                  }
                )
              }
            )
          }
        ),
        (0, import_jsx_runtime.jsx)(
          "svg",
          {
            width: "36",
            height: "38",
            viewBox: "0 0 36 38",
            fill: "none",
            xmlns: "http://www.w3.org/2000/svg",
            children: (0, import_jsx_runtime.jsx)(
              "path",
              {
                d: "M30.85 6.16832L22.2453 1.21782C19.4299 -0.405941 15.9801 -0.405941 13.1648 1.21782L4.52043 6.16832C1.74473 7.79208 0 10.802 0 14.0099V23.9505C0 27.198 1.74473 30.1683 4.52043 31.7921L13.1251 36.7822C15.9405 38.4059 19.3903 38.4059 22.2056 36.7822L30.8103 31.7921C33.6257 30.1683 35.3307 27.198 35.3307 23.9505V14.0099C35.41 10.802 33.6653 7.79208 30.85 6.16832ZM17.6852 27.8317C12.8079 27.8317 8.8426 23.8713 8.8426 19C8.8426 14.1287 12.8079 10.1683 17.6852 10.1683C22.5625 10.1683 26.5674 14.1287 26.5674 19C26.5674 23.8713 22.6022 27.8317 17.6852 27.8317Z",
                className: "fill-ui-button-inverted relative drop-shadow-sm"
              }
            )
          }
        )
      ]
    }
  );
};
var ResetPasswordInstructionsSchema = objectType({
  email: stringType().email()
});
var ResetPasswordSchema = objectType({
  password: stringType().min(1),
  repeat_password: stringType().min(1)
}).superRefine(({ password, repeat_password }, ctx) => {
  if (password !== repeat_password) {
    ctx.addIssue({
      code: ZodIssueCode.custom,
      message: instance.t("resetPassword.passwordMismatch"),
      path: ["repeat_password"]
    });
  }
});
var ResetPasswordTokenSchema = objectType({
  entity_id: stringType(),
  provider: stringType(),
  exp: numberType(),
  iat: numberType()
});
var validateDecodedResetPasswordToken = (decoded) => {
  return ResetPasswordTokenSchema.safeParse(decoded).success;
};
var InvalidResetToken = () => {
  const { t: t2 } = useTranslation();
  const navigate = useNavigate();
  return (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-base flex min-h-dvh w-dvw items-center justify-center", children: (0, import_jsx_runtime2.jsxs)("div", { className: "m-4 flex w-full max-w-[300px] flex-col items-center", children: [
    (0, import_jsx_runtime2.jsx)(LogoBox, { className: "mb-4" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "mb-6 flex flex-col items-center", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t2("resetPassword.invalidLinkTitle") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("resetPassword.invalidLinkHint") })
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full flex-col gap-y-3", children: (0, import_jsx_runtime2.jsx)(
      Button,
      {
        onClick: () => navigate("/reset-password", { replace: true }),
        className: "w-full",
        type: "submit",
        children: t2("resetPassword.goToResetPassword")
      }
    ) }),
    (0, import_jsx_runtime2.jsx)("span", { className: "txt-small my-6", children: (0, import_jsx_runtime2.jsx)(
      Trans,
      {
        i18nKey: "resetPassword.backToLogin",
        components: [
          (0, import_jsx_runtime2.jsx)(
            Link,
            {
              to: "/login",
              className: "text-ui-fg-interactive transition-fg hover:text-ui-fg-interactive-hover focus-visible:text-ui-fg-interactive-hover outline-none"
            },
            "login-link"
          )
        ]
      }
    ) })
  ] }) });
};
var ChooseNewPassword = ({ token }) => {
  const { t: t2 } = useTranslation();
  const [showAlert, setShowAlert] = (0, import_react.useState)(false);
  const invite = token ? l(token) : null;
  const isValidResetPasswordToken = invite && validateDecodedResetPasswordToken(invite);
  const form = useForm({
    resolver: t(ResetPasswordSchema),
    defaultValues: {
      password: "",
      repeat_password: ""
    }
  });
  const { mutateAsync, isPending } = useUpdateProviderForEmailPass(token);
  const handleSubmit = form.handleSubmit(async ({ password }) => {
    if (!invite) {
      return;
    }
    await mutateAsync(
      {
        password
      },
      {
        onSuccess: () => {
          form.setValue("password", "");
          form.setValue("repeat_password", "");
          setShowAlert(true);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  if (!isValidResetPasswordToken) {
    return (0, import_jsx_runtime2.jsx)(InvalidResetToken, {});
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-subtle flex min-h-dvh w-dvw items-center justify-center", children: (0, import_jsx_runtime2.jsxs)("div", { className: "m-4 flex w-full max-w-[280px] flex-col items-center", children: [
    (0, import_jsx_runtime2.jsx)(LogoBox, { className: "mb-4" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "mb-6 flex flex-col items-center", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t2("resetPassword.resetPassword") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("resetPassword.newPasswordHint") })
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full flex-col gap-y-3", children: (0, import_jsx_runtime2.jsx)(Form, { ...form, children: (0, import_jsx_runtime2.jsxs)(
      "form",
      {
        onSubmit: handleSubmit,
        className: "flex w-full flex-col gap-y-6",
        children: [
          (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime2.jsx)(Input, { type: "email", disabled: true, value: invite == null ? void 0 : invite.entity_id }),
            (0, import_jsx_runtime2.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "password",
                render: ({ field }) => {
                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      Input,
                      {
                        autoComplete: "new-password",
                        type: "password",
                        ...field,
                        placeholder: t2("resetPassword.newPassword")
                      }
                    ) }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime2.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "repeat_password",
                render: ({ field }) => {
                  return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                      Input,
                      {
                        autoComplete: "off",
                        type: "password",
                        ...field,
                        placeholder: t2("resetPassword.repeatNewPassword")
                      }
                    ) }),
                    (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          showAlert && (0, import_jsx_runtime2.jsx)(Alert, { dismissible: true, variant: "success", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
            (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-base mb-1", children: t2("resetPassword.successfulResetTitle") }),
            (0, import_jsx_runtime2.jsx)("span", { children: t2("resetPassword.successfulReset") })
          ] }) }),
          !showAlert && (0, import_jsx_runtime2.jsx)(Button, { className: "w-full", type: "submit", isLoading: isPending, children: t2("resetPassword.resetPassword") })
        ]
      }
    ) }) }),
    (0, import_jsx_runtime2.jsx)("span", { className: "txt-small my-6", children: (0, import_jsx_runtime2.jsx)(
      Trans,
      {
        i18nKey: "resetPassword.backToLogin",
        components: [
          (0, import_jsx_runtime2.jsx)(
            Link,
            {
              to: "/login",
              className: "text-ui-fg-base transition-fg hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover outline-none"
            },
            "login-link"
          )
        ]
      }
    ) })
  ] }) });
};
var ResetPassword = () => {
  const { t: t2 } = useTranslation();
  const [searchParams] = useSearchParams();
  const [showAlert, setShowAlert] = (0, import_react.useState)(false);
  const token = searchParams.get("token");
  const form = useForm({
    resolver: t(ResetPasswordInstructionsSchema),
    defaultValues: {
      email: ""
    }
  });
  const { mutateAsync, isPending } = useResetPasswordForEmailPass();
  const handleSubmit = form.handleSubmit(async ({ email }) => {
    await mutateAsync(
      {
        email
      },
      {
        onSuccess: () => {
          form.setValue("email", "");
          setShowAlert(true);
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  if (token) {
    return (0, import_jsx_runtime2.jsx)(ChooseNewPassword, { token });
  }
  return (0, import_jsx_runtime2.jsx)("div", { className: "bg-ui-bg-base flex min-h-dvh w-dvw items-center justify-center", children: (0, import_jsx_runtime2.jsxs)("div", { className: "m-4 flex w-full max-w-[300px] flex-col items-center", children: [
    (0, import_jsx_runtime2.jsx)(LogoBox, { className: "mb-4" }),
    (0, import_jsx_runtime2.jsxs)("div", { className: "mb-4 flex flex-col items-center", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { children: t2("resetPassword.resetPassword") }),
      (0, import_jsx_runtime2.jsx)(Text, { size: "small", className: "text-ui-fg-subtle text-center", children: t2("resetPassword.hint") })
    ] }),
    (0, import_jsx_runtime2.jsx)("div", { className: "flex w-full flex-col gap-y-3", children: (0, import_jsx_runtime2.jsx)(Form, { ...form, children: (0, import_jsx_runtime2.jsxs)(
      "form",
      {
        onSubmit: handleSubmit,
        className: "flex w-full flex-col gap-y-6",
        children: [
          (0, import_jsx_runtime2.jsx)("div", { className: "mt-4 flex flex-col gap-y-3", children: (0, import_jsx_runtime2.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "email",
              render: ({ field }) => {
                return (0, import_jsx_runtime2.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime2.jsx)(Form.Control, { children: (0, import_jsx_runtime2.jsx)(
                    Input,
                    {
                      autoComplete: "email",
                      ...field,
                      placeholder: t2("fields.email")
                    }
                  ) }),
                  (0, import_jsx_runtime2.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ) }),
          showAlert && (0, import_jsx_runtime2.jsx)(Alert, { dismissible: true, variant: "success", children: (0, import_jsx_runtime2.jsxs)("div", { className: "flex flex-col", children: [
            (0, import_jsx_runtime2.jsx)("span", { className: "text-ui-fg-base mb-1", children: t2("resetPassword.successfulRequestTitle") }),
            (0, import_jsx_runtime2.jsx)("span", { children: t2("resetPassword.successfulRequest") })
          ] }) }),
          (0, import_jsx_runtime2.jsx)(Button, { className: "w-full", type: "submit", isLoading: isPending, children: t2("resetPassword.sendResetInstructions") })
        ]
      }
    ) }) }),
    (0, import_jsx_runtime2.jsx)("span", { className: "txt-small my-6", children: (0, import_jsx_runtime2.jsx)(
      Trans,
      {
        i18nKey: "resetPassword.backToLogin",
        components: [
          (0, import_jsx_runtime2.jsx)(
            Link,
            {
              to: "/login",
              className: "text-ui-fg-base transition-fg hover:text-ui-fg-base-hover focus-visible:text-ui-fg-base-hover outline-none"
            },
            "login-link"
          )
        ]
      }
    ) })
  ] }) });
};
export {
  ResetPassword as Component
};
//# sourceMappingURL=reset-password-UUL5N3DT-4HWEESOZ.js.map
