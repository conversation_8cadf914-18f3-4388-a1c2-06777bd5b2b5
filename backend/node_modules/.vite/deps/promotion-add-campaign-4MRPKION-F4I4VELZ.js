import {
  AddCampaignPromotionForm
} from "./chunk-N7P7IQZN.js";
import "./chunk-HP4EYY5H.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-DP54EP6X.js";
import {
  RouteDrawer
} from "./chunk-L2KJ2QJA.js";
import "./chunk-7FOP5RO4.js";
import "./chunk-4XXECALA.js";
import "./chunk-IL7M46GI.js";
import {
  useCampaigns,
  usePromotion
} from "./chunk-CN6R4DBW.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Heading
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-add-campaign-4MRPKION.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PromotionAddCampaign = () => {
  var _a, _b;
  const { id } = useParams();
  const { t } = useTranslation();
  const { promotion, isPending, isError, error } = usePromotion(id);
  let campaignQuery = {};
  if ((_a = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _a.currency_code) {
    campaignQuery = {
      budget: {
        currency_code: (_b = promotion == null ? void 0 : promotion.application_method) == null ? void 0 : _b.currency_code
      }
    };
  }
  const {
    campaigns,
    isPending: areCampaignsLoading,
    isError: isCampaignError,
    error: campaignError
  } = useCampaigns(campaignQuery);
  if (isError || isCampaignError) {
    throw error || campaignError;
  }
  return (0, import_jsx_runtime.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime.jsx)(Heading, { children: t("promotions.campaign.edit.header") }) }),
    !isPending && !areCampaignsLoading && promotion && campaigns && (0, import_jsx_runtime.jsx)(AddCampaignPromotionForm, { promotion, campaigns })
  ] });
};
export {
  PromotionAddCampaign as Component
};
//# sourceMappingURL=promotion-add-campaign-4MRPKION-F4I4VELZ.js.map
