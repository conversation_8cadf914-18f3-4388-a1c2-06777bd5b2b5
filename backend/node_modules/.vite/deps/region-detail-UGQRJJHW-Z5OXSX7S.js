import {
  useCountries,
  useCountryTableColumns,
  useCountryTableQuery
} from "./chunk-MB4L63P5.js";
import {
  ListSummary
} from "./chunk-2BWFXI2Q.js";
import {
  formatProvider
} from "./chunk-LVAKEKGS.js";
import {
  SectionRow
} from "./chunk-MGJVSCJZ.js";
import "./chunk-EGRHWZRV.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import {
  currencies
} from "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  regionsQueryKeys,
  useDeleteRegion,
  useRegion,
  useUpdateRegion
} from "./chunk-NWAMKOL4.js";
import {
  usePricePreferences
} from "./chunk-6TPPQSEA.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useNavigate,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  Checkbox,
  Container,
  Heading,
  PencilSquare,
  PlusMini,
  Text,
  Trash,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/region-detail-UGQRJJHW.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var REGION_DETAIL_FIELDS = "*payment_providers,*countries,+automatic_taxes";
var RegionDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { region } = useRegion(
    id,
    {
      fields: REGION_DETAIL_FIELDS
    },
    {
      initialData: props.data,
      enabled: Boolean(id)
    }
  );
  if (!region) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: region.name });
};
var regionQuery = (id) => ({
  queryKey: regionsQueryKeys.detail(id),
  queryFn: async () => sdk.admin.region.retrieve(id, {
    fields: REGION_DETAIL_FIELDS
  })
});
var regionLoader = async ({ params }) => {
  const id = params.id;
  const query = regionQuery(id);
  return queryClient.getQueryData(
    query.queryKey
  ) ?? await queryClient.fetchQuery(query);
};
var PREFIX = "c";
var PAGE_SIZE = 10;
var RegionCountrySection = ({ region }) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { searchParams, raw } = useCountryTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX
  });
  const { countries, count } = useCountries({
    countries: region.countries || [],
    ...searchParams
  });
  const columns = useColumns();
  const { table } = useDataTable({
    data: countries || [],
    columns,
    count,
    enablePagination: true,
    enableRowSelection: true,
    getRowId: (row) => row.iso_2,
    pageSize: PAGE_SIZE,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    prefix: PREFIX,
    meta: {
      region
    }
  });
  const { mutateAsync } = useUpdateRegion(region.id);
  const handleRemoveCountries = async () => {
    var _a;
    const ids = Object.keys(rowSelection).filter((k) => rowSelection[k]);
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("regions.removeCountriesWarning", {
        count: ids.length
      }),
      verificationText: t("actions.remove"),
      verificationInstruction: t("general.typeToConfirm"),
      cancelText: t("actions.cancel"),
      confirmText: t("actions.remove")
    });
    if (!res) {
      return;
    }
    const payload = (_a = region.countries) == null ? void 0 : _a.filter((c) => !ids.includes(c.iso_2)).map((c) => c.iso_2);
    await mutateAsync(
      {
        countries: payload
      },
      {
        onSuccess: () => {
          toast.success(t("regions.toast.countries"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("fields.countries") }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  label: t("regions.addCountries"),
                  icon: (0, import_jsx_runtime2.jsx)(PlusMini, {}),
                  to: "countries/add"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        table,
        columns,
        pageSize: PAGE_SIZE,
        count,
        orderBy: [
          { key: "display_name", label: t("fields.name") },
          { key: "iso_2", label: t("fields.code") }
        ],
        search: true,
        pagination: true,
        queryObject: raw,
        prefix: PREFIX,
        commands: [
          {
            action: handleRemoveCountries,
            label: t("actions.remove"),
            shortcut: "r"
          }
        ]
      }
    )
  ] });
};
var CountryActions = ({
  country,
  region
}) => {
  var _a;
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { mutateAsync } = useUpdateRegion(region.id);
  const payload = (_a = region.countries) == null ? void 0 : _a.filter((c) => c.iso_2 !== country.iso_2).map((c) => c.iso_2);
  const handleRemove = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("regions.removeCountryWarning", {
        name: country.display_name
      }),
      verificationText: country.display_name,
      verificationInstruction: t("general.typeToConfirm"),
      cancelText: t("actions.cancel"),
      confirmText: t("actions.remove")
    });
    if (!res) {
      return;
    }
    await mutateAsync(
      {
        countries: payload
      },
      {
        onSuccess: () => {
          toast.success(t("regions.toast.countries"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime2.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              label: t("actions.remove"),
              onClick: handleRemove,
              icon: (0, import_jsx_runtime2.jsx)(Trash, {})
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCountryTableColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row, table }) => {
          const { region } = table.options.meta;
          return (0, import_jsx_runtime2.jsx)(CountryActions, { country: row.original, region });
        }
      })
    ],
    [base]
  );
};
var RegionGeneralSection = ({
  region,
  pricePreferences
}) => {
  var _a;
  const { t } = useTranslation();
  const pricePreferenceForRegion = pricePreferences == null ? void 0 : pricePreferences.find(
    (preference) => preference.attribute === "region_id" && preference.value === region.id
  );
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime3.jsx)(Heading, { children: region.name }),
      (0, import_jsx_runtime3.jsx)(RegionActions, { region })
    ] }),
    (0, import_jsx_runtime3.jsx)(
      SectionRow,
      {
        title: t("fields.currency"),
        value: (0, import_jsx_runtime3.jsxs)("div", { className: "flex items-center gap-x-2", children: [
          (0, import_jsx_runtime3.jsx)(Badge, { size: "2xsmall", className: "uppercase", children: region.currency_code }),
          (0, import_jsx_runtime3.jsx)(Text, { size: "small", leading: "compact", children: currencies[region.currency_code.toUpperCase()].name })
        ] })
      }
    ),
    (0, import_jsx_runtime3.jsx)(
      SectionRow,
      {
        title: t("fields.automaticTaxes"),
        value: region.automatic_taxes ? t("fields.true") : t("fields.false")
      }
    ),
    (0, import_jsx_runtime3.jsx)(
      SectionRow,
      {
        title: t("fields.taxInclusivePricing"),
        value: (pricePreferenceForRegion == null ? void 0 : pricePreferenceForRegion.is_tax_inclusive) ? t("fields.true") : t("fields.false")
      }
    ),
    (0, import_jsx_runtime3.jsx)(
      SectionRow,
      {
        title: t("fields.paymentProviders"),
        value: (0, import_jsx_runtime3.jsx)("div", { className: "inline-flex", children: ((_a = region.payment_providers) == null ? void 0 : _a.length) ? (0, import_jsx_runtime3.jsx)(
          ListSummary,
          {
            list: region.payment_providers.map((p) => formatProvider(p.id))
          }
        ) : "-" })
      }
    )
  ] });
};
var RegionActions = ({ region }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { mutateAsync } = useDeleteRegion(region.id);
  const prompt = usePrompt();
  const handleDelete = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("regions.deleteRegionWarning", {
        name: region.name
      }),
      verificationText: region.name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel")
    });
    if (!res) {
      return;
    }
    await mutateAsync(void 0, {
      onSuccess: () => {
        toast.success(t("regions.toast.delete"));
        navigate("/settings/regions", { replace: true });
      },
      onError: (e) => {
        toast.error(e.message);
      }
    });
  };
  return (0, import_jsx_runtime3.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime3.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `/settings/regions/${region.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime3.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var RegionDetail = () => {
  const initialData = useLoaderData();
  const { id } = useParams();
  const {
    region,
    isPending: isLoading,
    isError: isRegionError,
    error: regionError
  } = useRegion(
    id,
    { fields: REGION_DETAIL_FIELDS },
    {
      initialData
    }
  );
  const {
    price_preferences: pricePreferences,
    isPending: isLoadingPreferences,
    isError: isPreferencesError,
    error: preferencesError
  } = usePricePreferences(
    {
      attribute: "region_id",
      value: id
    },
    { enabled: !!region }
  );
  const { getWidgets } = useExtension();
  if (isLoading || isLoadingPreferences || !region) {
    return (0, import_jsx_runtime4.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });
  }
  if (isRegionError) {
    throw regionError;
  }
  if (isPreferencesError) {
    throw preferencesError;
  }
  return (0, import_jsx_runtime4.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("region.details.before"),
        after: getWidgets("region.details.after")
      },
      data: region,
      showMetadata: true,
      showJSON: true,
      children: [
        (0, import_jsx_runtime4.jsx)(
          RegionGeneralSection,
          {
            region,
            pricePreferences: pricePreferences ?? []
          }
        ),
        (0, import_jsx_runtime4.jsx)(RegionCountrySection, { region })
      ]
    }
  );
};
export {
  RegionDetailBreadcrumb as Breadcrumb,
  RegionDetail as Component,
  regionLoader as loader
};
//# sourceMappingURL=region-detail-UGQRJJHW-Z5OXSX7S.js.map
