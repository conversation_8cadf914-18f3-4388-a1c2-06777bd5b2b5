{"version": 3, "sources": ["../../@medusajs/dashboard/dist/shipping-profile-detail-FDUPDSH4.mjs"], "sourcesContent": ["import \"./chunk-YOYOJU5D.mjs\";\nimport {\n  SectionRow\n} from \"./chunk-LFLGEXIG.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport {\n  SingleColumnPageSkeleton\n} from \"./chunk-LPEUYMRK.mjs\";\nimport {\n  ActionMenu\n} from \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport {\n  shippingProfileQueryKeys,\n  useDeleteShippingProfile,\n  useShippingProfile\n} from \"./chunk-PIR2H25N.mjs\";\nimport {\n  queryClient\n} from \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/shipping-profiles/shipping-profile-detail/breadcrumb.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar ShippingProfileDetailBreadcrumb = (props) => {\n  const { shipping_profile_id } = props.params || {};\n  const { shipping_profile } = useShippingProfile(\n    shipping_profile_id,\n    void 0,\n    {\n      initialData: props.data,\n      enabled: Boolean(shipping_profile_id)\n    }\n  );\n  if (!shipping_profile) {\n    return null;\n  }\n  return /* @__PURE__ */ jsx(\"span\", { children: shipping_profile.name });\n};\n\n// src/routes/shipping-profiles/shipping-profile-detail/loader.ts\nvar shippingProfileQuery = (id) => ({\n  queryKey: shippingProfileQueryKeys.detail(id),\n  queryFn: async () => sdk.admin.shippingProfile.retrieve(id)\n});\nvar shippingProfileLoader = async ({ params }) => {\n  const id = params.shipping_profile_id;\n  const query = shippingProfileQuery(id);\n  return queryClient.ensureQueryData(query);\n};\n\n// src/routes/shipping-profiles/shipping-profile-detail/shipping-profile-detail.tsx\nimport { useLoaderData, useParams } from \"react-router-dom\";\n\n// src/routes/shipping-profiles/shipping-profile-detail/components/shipping-profile-general-section/shipping-profile-general-section.tsx\nimport { Trash } from \"@medusajs/icons\";\nimport { Container, Heading, toast, usePrompt } from \"@medusajs/ui\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar ShippingProfileGeneralSection = ({\n  profile\n}) => {\n  const { t } = useTranslation();\n  const prompt = usePrompt();\n  const navigate = useNavigate();\n  const { mutateAsync } = useDeleteShippingProfile(profile.id);\n  const handleDelete = async () => {\n    const res = await prompt({\n      title: t(\"shippingProfile.delete.title\"),\n      description: t(\"shippingProfile.delete.description\", {\n        name: profile.name\n      }),\n      verificationText: profile.name,\n      verificationInstruction: t(\"general.typeToConfirm\"),\n      confirmText: t(\"actions.delete\"),\n      cancelText: t(\"actions.cancel\")\n    });\n    if (!res) {\n      return;\n    }\n    await mutateAsync(void 0, {\n      onSuccess: () => {\n        toast.success(\n          t(\"shippingProfile.delete.successToast\", {\n            name: profile.name\n          })\n        );\n        navigate(\"/settings/locations/shipping-profiles\", { replace: true });\n      },\n      onError: (error) => {\n        toast.error(error.message);\n      }\n    });\n  };\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: [\n      /* @__PURE__ */ jsx2(Heading, { children: profile.name }),\n      /* @__PURE__ */ jsx2(\n        ActionMenu,\n        {\n          groups: [\n            {\n              actions: [\n                {\n                  icon: /* @__PURE__ */ jsx2(Trash, {}),\n                  label: t(\"actions.delete\"),\n                  onClick: handleDelete\n                }\n              ]\n            }\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ jsx2(SectionRow, { title: t(\"fields.type\"), value: profile.type })\n  ] });\n};\n\n// src/routes/shipping-profiles/shipping-profile-detail/shipping-profile-detail.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar ShippingProfileDetail = () => {\n  const { shipping_profile_id } = useParams();\n  const initialData = useLoaderData();\n  const { shipping_profile, isLoading, isError, error } = useShippingProfile(\n    shipping_profile_id,\n    void 0,\n    { initialData }\n  );\n  const { getWidgets } = useExtension();\n  if (isLoading || !shipping_profile) {\n    return /* @__PURE__ */ jsx3(SingleColumnPageSkeleton, { sections: 1, showJSON: true, showMetadata: true });\n  }\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      widgets: {\n        before: getWidgets(\"shipping_profile.details.before\"),\n        after: getWidgets(\"shipping_profile.details.after\")\n      },\n      showMetadata: true,\n      showJSON: true,\n      data: shipping_profile,\n      children: /* @__PURE__ */ jsx3(ShippingProfileGeneralSection, { profile: shipping_profile })\n    }\n  );\n};\nexport {\n  ShippingProfileDetailBreadcrumb as Breadcrumb,\n  ShippingProfileDetail as Component,\n  shippingProfileLoader as loader\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,yBAAoB;AAoCpB,IAAAA,sBAAkC;AA6DlC,IAAAA,sBAA4B;AAhG5B,IAAI,kCAAkC,CAAC,UAAU;AAC/C,QAAM,EAAE,oBAAoB,IAAI,MAAM,UAAU,CAAC;AACjD,QAAM,EAAE,iBAAiB,IAAI;AAAA,IAC3B;AAAA,IACA;AAAA,IACA;AAAA,MACE,aAAa,MAAM;AAAA,MACnB,SAAS,QAAQ,mBAAmB;AAAA,IACtC;AAAA,EACF;AACA,MAAI,CAAC,kBAAkB;AACrB,WAAO;AAAA,EACT;AACA,aAAuB,wBAAI,QAAQ,EAAE,UAAU,iBAAiB,KAAK,CAAC;AACxE;AAGA,IAAI,uBAAuB,CAAC,QAAQ;AAAA,EAClC,UAAU,yBAAyB,OAAO,EAAE;AAAA,EAC5C,SAAS,YAAY,IAAI,MAAM,gBAAgB,SAAS,EAAE;AAC5D;AACA,IAAI,wBAAwB,OAAO,EAAE,OAAO,MAAM;AAChD,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,qBAAqB,EAAE;AACrC,SAAO,YAAY,gBAAgB,KAAK;AAC1C;AAWA,IAAI,gCAAgC,CAAC;AAAA,EACnC;AACF,MAAM;AACJ,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,SAAS,UAAU;AACzB,QAAM,WAAW,YAAY;AAC7B,QAAM,EAAE,YAAY,IAAI,yBAAyB,QAAQ,EAAE;AAC3D,QAAM,eAAe,YAAY;AAC/B,UAAM,MAAM,MAAM,OAAO;AAAA,MACvB,OAAO,EAAE,8BAA8B;AAAA,MACvC,aAAa,EAAE,sCAAsC;AAAA,QACnD,MAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,MACD,kBAAkB,QAAQ;AAAA,MAC1B,yBAAyB,EAAE,uBAAuB;AAAA,MAClD,aAAa,EAAE,gBAAgB;AAAA,MAC/B,YAAY,EAAE,gBAAgB;AAAA,IAChC,CAAC;AACD,QAAI,CAAC,KAAK;AACR;AAAA,IACF;AACA,UAAM,YAAY,QAAQ;AAAA,MACxB,WAAW,MAAM;AACf,cAAM;AAAA,UACJ,EAAE,uCAAuC;AAAA,YACvC,MAAM,QAAQ;AAAA,UAChB,CAAC;AAAA,QACH;AACA,iBAAS,yCAAyC,EAAE,SAAS,KAAK,CAAC;AAAA,MACrE;AAAA,MACA,SAAS,CAAC,UAAU;AAClB,cAAM,MAAM,MAAM,OAAO;AAAA,MAC3B;AAAA,IACF,CAAC;AAAA,EACH;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,0BAAK,OAAO,EAAE,WAAW,+CAA+C,UAAU;AAAA,UAChF,oBAAAC,KAAK,SAAS,EAAE,UAAU,QAAQ,KAAK,CAAC;AAAA,UACxC,oBAAAA;AAAA,QACd;AAAA,QACA;AAAA,UACE,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP;AAAA,kBACE,UAAsB,oBAAAA,KAAK,OAAO,CAAC,CAAC;AAAA,kBACpC,OAAO,EAAE,gBAAgB;AAAA,kBACzB,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,EAAE,CAAC;AAAA,QACa,oBAAAA,KAAK,YAAY,EAAE,OAAO,EAAE,aAAa,GAAG,OAAO,QAAQ,KAAK,CAAC;AAAA,EACnF,EAAE,CAAC;AACL;AAIA,IAAI,wBAAwB,MAAM;AAChC,QAAM,EAAE,oBAAoB,IAAI,UAAU;AAC1C,QAAM,cAAc,cAAc;AAClC,QAAM,EAAE,kBAAkB,WAAW,SAAS,MAAM,IAAI;AAAA,IACtD;AAAA,IACA;AAAA,IACA,EAAE,YAAY;AAAA,EAChB;AACA,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,MAAI,aAAa,CAAC,kBAAkB;AAClC,eAAuB,oBAAAC,KAAK,0BAA0B,EAAE,UAAU,GAAG,UAAU,MAAM,cAAc,KAAK,CAAC;AAAA,EAC3G;AACA,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAA;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,QAAQ,WAAW,iCAAiC;AAAA,QACpD,OAAO,WAAW,gCAAgC;AAAA,MACpD;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,MAAM;AAAA,MACN,cAA0B,oBAAAA,KAAK,+BAA+B,EAAE,SAAS,iBAAiB,CAAC;AAAA,IAC7F;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}