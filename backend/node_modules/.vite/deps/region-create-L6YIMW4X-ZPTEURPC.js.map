{"version": 3, "sources": ["../../@medusajs/dashboard/dist/region-create-L6YIMW4X.mjs"], "sourcesContent": ["import {\n  useCountries,\n  useCountryTableColumns,\n  useCountryTableQuery\n} from \"./chunk-NOAFLTPV.mjs\";\nimport {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-B2JT2FOA.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport {\n  currencies\n} from \"./chunk-MWVM4TYO.mjs\";\nimport \"./chunk-GJUPECDU.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteFocusModal,\n  StackedFocusModal,\n  useRouteModal,\n  useStackedModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  countries\n} from \"./chunk-DG7J63J2.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport {\n  usePaymentProviders\n} from \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport {\n  useStore\n} from \"./chunk-V2LANK5S.mjs\";\nimport {\n  useCreateRegion\n} from \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/regions/region-create/components/create-region-form/create-region-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { XMarkMini } from \"@medusajs/icons\";\nimport {\n  Button,\n  Checkbox,\n  Heading,\n  Input,\n  Select,\n  Switch,\n  Text,\n  clx,\n  toast\n} from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo, useState } from \"react\";\nimport { useForm, useWatch } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport * as zod from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar CreateRegionSchema = zod.object({\n  name: zod.string().min(1),\n  currency_code: zod.string().min(2, \"Select a currency\"),\n  automatic_taxes: zod.boolean(),\n  is_tax_inclusive: zod.boolean(),\n  countries: zod.array(zod.object({ code: zod.string(), name: zod.string() })),\n  payment_providers: zod.array(zod.string()).min(1)\n});\nvar PREFIX = \"cr\";\nvar PAGE_SIZE = 50;\nvar STACKED_MODAL_ID = \"countries-modal\";\nvar CreateRegionForm = ({\n  currencies: currencies2,\n  paymentProviders\n}) => {\n  const { setIsOpen } = useStackedModal();\n  const [rowSelection, setRowSelection] = useState({});\n  const { handleSuccess } = useRouteModal();\n  const form = useForm({\n    defaultValues: {\n      name: \"\",\n      currency_code: \"\",\n      automatic_taxes: true,\n      is_tax_inclusive: false,\n      countries: [],\n      payment_providers: []\n    },\n    resolver: zodResolver(CreateRegionSchema)\n  });\n  const selectedCountries = useWatch({\n    control: form.control,\n    name: \"countries\",\n    defaultValue: []\n  });\n  const { t } = useTranslation();\n  const { mutateAsync: createRegion, isPending: isPendingRegion } = useCreateRegion();\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await createRegion(\n      {\n        name: values.name,\n        countries: values.countries.map((c) => c.code),\n        currency_code: values.currency_code,\n        payment_providers: values.payment_providers,\n        automatic_taxes: values.automatic_taxes,\n        is_tax_inclusive: values.is_tax_inclusive\n      },\n      {\n        onSuccess: ({ region }) => {\n          toast.success(t(\"regions.toast.create\"));\n          handleSuccess(`../${region.id}`);\n        },\n        onError: (e) => {\n          toast.error(e.message);\n        }\n      }\n    );\n  });\n  const { searchParams, raw } = useCountryTableQuery({\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const { countries: countries2, count } = useCountries({\n    countries: countries.map((c, i) => ({\n      display_name: c.display_name,\n      name: c.name,\n      id: i,\n      iso_2: c.iso_2,\n      iso_3: c.iso_3,\n      num_code: c.num_code,\n      region_id: null,\n      region: {}\n    })),\n    ...searchParams\n  });\n  const columns = useColumns();\n  const { table } = useDataTable({\n    data: countries2 || [],\n    columns,\n    count,\n    enablePagination: true,\n    enableRowSelection: true,\n    rowSelection: {\n      state: rowSelection,\n      updater: setRowSelection\n    },\n    getRowId: (row) => row.iso_2,\n    pageSize: PAGE_SIZE,\n    prefix: PREFIX\n  });\n  const saveCountries = () => {\n    const selected = Object.keys(rowSelection).filter(\n      (key) => rowSelection[key]\n    );\n    form.setValue(\n      \"countries\",\n      selected.map((key) => ({\n        code: key,\n        name: countries.find((c) => c.iso_2 === key).display_name\n      })),\n      { shouldDirty: true, shouldTouch: true }\n    );\n    setIsOpen(STACKED_MODAL_ID, false);\n  };\n  const removeCountry = (code) => {\n    const update = selectedCountries.filter((c) => c.code !== code);\n    const ids = update.map((c) => c.code).reduce((acc, c) => {\n      acc[c] = true;\n      return acc;\n    }, {});\n    form.setValue(\"countries\", update, { shouldDirty: true, shouldTouch: true });\n    setRowSelection(ids);\n  };\n  const clearCountries = () => {\n    form.setValue(\"countries\", [], { shouldDirty: true, shouldTouch: true });\n    setRowSelection({});\n  };\n  return /* @__PURE__ */ jsx(RouteFocusModal.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex h-full flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteFocusModal.Header, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPendingRegion, children: t(\"actions.save\") })\n        ] }) }),\n        /* @__PURE__ */ jsx(RouteFocusModal.Body, { className: \"flex overflow-hidden\", children: /* @__PURE__ */ jsx(\n          \"div\",\n          {\n            className: clx(\n              \"flex h-full w-full flex-col items-center overflow-y-auto p-16\"\n            ),\n            id: \"form-section\",\n            children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex w-full max-w-[720px] flex-col gap-y-8\", children: [\n              /* @__PURE__ */ jsxs(\"div\", { children: [\n                /* @__PURE__ */ jsx(Heading, { children: t(\"regions.createRegion\") }),\n                /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"regions.createRegionHint\") })\n              ] }),\n              /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: [\n                /* @__PURE__ */ jsx(\n                  Form.Field,\n                  {\n                    control: form.control,\n                    name: \"name\",\n                    render: ({ field }) => {\n                      return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.name\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(Input, { ...field }) }),\n                        /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                      ] });\n                    }\n                  }\n                ),\n                /* @__PURE__ */ jsx(\n                  Form.Field,\n                  {\n                    control: form.control,\n                    name: \"currency_code\",\n                    render: ({ field: { onChange, ref, ...field } }) => {\n                      return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.currency\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsxs(Select, { ...field, onValueChange: onChange, children: [\n                          /* @__PURE__ */ jsx(Select.Trigger, { ref, children: /* @__PURE__ */ jsx(Select.Value, {}) }),\n                          /* @__PURE__ */ jsx(Select.Content, { children: currencies2.map((currency) => /* @__PURE__ */ jsx(\n                            Select.Item,\n                            {\n                              value: currency.code,\n                              children: currency.name\n                            },\n                            currency.code\n                          )) })\n                        ] }) }),\n                        /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                      ] });\n                    }\n                  }\n                )\n              ] }) }),\n              /* @__PURE__ */ jsx(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: \"automatic_taxes\",\n                  render: ({ field: { value, onChange, ...field } }) => {\n                    return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(\"div\", { children: [\n                      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-start justify-between\", children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.automaticTaxes\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                          Switch,\n                          {\n                            ...field,\n                            checked: value,\n                            onCheckedChange: onChange\n                          }\n                        ) })\n                      ] }),\n                      /* @__PURE__ */ jsx(Form.Hint, { children: t(\"regions.automaticTaxesHint\") }),\n                      /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                    ] }) });\n                  }\n                }\n              ),\n              /* @__PURE__ */ jsx(\n                Form.Field,\n                {\n                  control: form.control,\n                  name: \"is_tax_inclusive\",\n                  render: ({ field: { value, onChange, ...field } }) => {\n                    return /* @__PURE__ */ jsx(Form.Item, { children: /* @__PURE__ */ jsxs(\"div\", { children: [\n                      /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-start justify-between\", children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.taxInclusivePricing\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                          Switch,\n                          {\n                            ...field,\n                            checked: value,\n                            onCheckedChange: onChange\n                          }\n                        ) })\n                      ] }),\n                      /* @__PURE__ */ jsx(Form.Hint, { children: t(\"regions.taxInclusiveHint\") }),\n                      /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                    ] }) });\n                  }\n                }\n              ),\n              /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-base h-px w-full\" }),\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n                /* @__PURE__ */ jsxs(\"div\", { children: [\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.countries\") }),\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"regions.countriesHint\") })\n                ] }),\n                selectedCountries.length > 0 && /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-wrap gap-2\", children: [\n                  selectedCountries.map((country) => /* @__PURE__ */ jsx(\n                    CountryTag,\n                    {\n                      country,\n                      onRemove: removeCountry\n                    },\n                    country.code\n                  )),\n                  /* @__PURE__ */ jsx(\n                    Button,\n                    {\n                      variant: \"transparent\",\n                      size: \"small\",\n                      className: \"text-ui-fg-muted hover:text-ui-fg-subtle\",\n                      onClick: clearCountries,\n                      children: t(\"actions.clearAll\")\n                    }\n                  )\n                ] }),\n                /* @__PURE__ */ jsxs(StackedFocusModal, { id: STACKED_MODAL_ID, children: [\n                  /* @__PURE__ */ jsx(\"div\", { className: \"flex items-center justify-end\", children: /* @__PURE__ */ jsx(StackedFocusModal.Trigger, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"regions.addCountries\") }) }) }),\n                  /* @__PURE__ */ jsx(StackedFocusModal.Content, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex size-full flex-col overflow-hidden\", children: [\n                    /* @__PURE__ */ jsx(StackedFocusModal.Header, { children: /* @__PURE__ */ jsx(StackedFocusModal.Title, { asChild: true, children: /* @__PURE__ */ jsx(\"span\", { className: \"sr-only\", children: t(\"regions.addCountries\") }) }) }),\n                    /* @__PURE__ */ jsx(StackedFocusModal.Body, { className: \"overflow-hidden\", children: /* @__PURE__ */ jsx(\n                      _DataTable,\n                      {\n                        table,\n                        columns,\n                        count,\n                        pageSize: PAGE_SIZE,\n                        orderBy: [\n                          { key: \"display_name\", label: t(\"fields.name\") },\n                          { key: \"iso_2\", label: t(\"fields.code\") }\n                        ],\n                        pagination: true,\n                        search: \"autofocus\",\n                        layout: \"fill\",\n                        queryObject: raw,\n                        prefix: PREFIX\n                      }\n                    ) }),\n                    /* @__PURE__ */ jsx(StackedFocusModal.Footer, { children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n                      /* @__PURE__ */ jsx(StackedFocusModal.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { variant: \"secondary\", size: \"small\", children: t(\"actions.cancel\") }) }),\n                      /* @__PURE__ */ jsx(\n                        Button,\n                        {\n                          size: \"small\",\n                          type: \"button\",\n                          onClick: saveCountries,\n                          children: t(\"actions.save\")\n                        }\n                      )\n                    ] }) })\n                  ] }) })\n                ] })\n              ] }),\n              /* @__PURE__ */ jsx(\"div\", { className: \"bg-ui-border-base h-px w-full\" }),\n              /* @__PURE__ */ jsxs(\"div\", { className: \"flex flex-col gap-y-4\", children: [\n                /* @__PURE__ */ jsxs(\"div\", { children: [\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", leading: \"compact\", weight: \"plus\", children: t(\"fields.providers\") }),\n                  /* @__PURE__ */ jsx(Text, { size: \"small\", className: \"text-ui-fg-subtle\", children: t(\"regions.providersHint\") })\n                ] }),\n                /* @__PURE__ */ jsx(\"div\", { className: \"grid grid-cols-1 gap-4 md:grid-cols-2\", children: /* @__PURE__ */ jsx(\n                  Form.Field,\n                  {\n                    control: form.control,\n                    name: \"payment_providers\",\n                    render: ({ field }) => {\n                      return /* @__PURE__ */ jsxs(Form.Item, { children: [\n                        /* @__PURE__ */ jsx(Form.Label, { children: t(\"fields.paymentProviders\") }),\n                        /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                          Combobox,\n                          {\n                            options: paymentProviders.map((pp) => ({\n                              label: formatProvider(pp.id),\n                              value: pp.id\n                            })),\n                            ...field\n                          }\n                        ) }),\n                        /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n                      ] });\n                    }\n                  }\n                ) })\n              ] })\n            ] })\n          }\n        ) })\n      ]\n    }\n  ) });\n};\nvar columnHelper = createColumnHelper();\nvar useColumns = () => {\n  const base = useCountryTableColumns();\n  return useMemo(\n    () => [\n      columnHelper.display({\n        id: \"select\",\n        header: ({ table }) => {\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: table.getIsSomePageRowsSelected() ? \"indeterminate\" : table.getIsAllPageRowsSelected(),\n              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)\n            }\n          );\n        },\n        cell: ({ row }) => {\n          const isPreselected = !row.getCanSelect();\n          return /* @__PURE__ */ jsx(\n            Checkbox,\n            {\n              checked: row.getIsSelected() || isPreselected,\n              disabled: isPreselected,\n              onCheckedChange: (value) => row.toggleSelected(!!value),\n              onClick: (e) => {\n                e.stopPropagation();\n              }\n            }\n          );\n        }\n      }),\n      ...base\n    ],\n    [base]\n  );\n};\nvar CountryTag = ({\n  country,\n  onRemove\n}) => {\n  return /* @__PURE__ */ jsxs(\"div\", { className: \"bg-ui-bg-field shadow-borders-base transition-fg hover:bg-ui-bg-field-hover flex h-7 items-center overflow-hidden rounded-md\", children: [\n    /* @__PURE__ */ jsx(\"div\", { className: \"txt-compact-small-plus flex h-full select-none items-center justify-center px-2 py-0.5\", children: country.name }),\n    /* @__PURE__ */ jsx(\n      \"button\",\n      {\n        type: \"button\",\n        onClick: () => onRemove(country.code),\n        className: \"focus-visible:bg-ui-bg-field-hover transition-fg hover:bg-ui-bg-field-hover flex h-full w-7 items-center justify-center border-l outline-none\",\n        children: /* @__PURE__ */ jsx(XMarkMini, { className: \"text-ui-fg-muted\" })\n      }\n    )\n  ] });\n};\n\n// src/routes/regions/region-create/region-create.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar RegionCreate = () => {\n  const { store, isPending: isLoading, isError, error } = useStore();\n  const storeCurrencies = (store?.supported_currencies ?? []).map(\n    (c) => currencies[c.currency_code.toUpperCase()]\n  );\n  const { payment_providers: paymentProviders = [] } = usePaymentProviders({\n    is_enabled: true\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx2(RouteFocusModal, { children: !isLoading && store && /* @__PURE__ */ jsx2(\n    CreateRegionForm,\n    {\n      currencies: storeCurrencies,\n      paymentProviders\n    }\n  ) });\n};\nexport {\n  RegionCreate as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA,mBAAkC;AAIlC,yBAA0B;AA8X1B,IAAAA,sBAA4B;AA7X5B,IAAI,qBAAyB,WAAO;AAAA,EAClC,MAAU,WAAO,EAAE,IAAI,CAAC;AAAA,EACxB,eAAmB,WAAO,EAAE,IAAI,GAAG,mBAAmB;AAAA,EACtD,iBAAqB,YAAQ;AAAA,EAC7B,kBAAsB,YAAQ;AAAA,EAC9B,WAAe,UAAU,WAAO,EAAE,MAAU,WAAO,GAAG,MAAU,WAAO,EAAE,CAAC,CAAC;AAAA,EAC3E,mBAAuB,UAAU,WAAO,CAAC,EAAE,IAAI,CAAC;AAClD,CAAC;AACD,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,CAAC;AAAA,EACtB,YAAY;AAAA,EACZ;AACF,MAAM;AACJ,QAAM,EAAE,UAAU,IAAI,gBAAgB;AACtC,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,CAAC,CAAC;AACnD,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,MAAM;AAAA,MACN,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,WAAW,CAAC;AAAA,MACZ,mBAAmB,CAAC;AAAA,IACtB;AAAA,IACA,UAAU,EAAY,kBAAkB;AAAA,EAC1C,CAAC;AACD,QAAM,oBAAoB,SAAS;AAAA,IACjC,SAAS,KAAK;AAAA,IACd,MAAM;AAAA,IACN,cAAc,CAAC;AAAA,EACjB,CAAC;AACD,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,aAAa,cAAc,WAAW,gBAAgB,IAAI,gBAAgB;AAClF,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,MAAM,OAAO;AAAA,QACb,WAAW,OAAO,UAAU,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,QAC7C,eAAe,OAAO;AAAA,QACtB,mBAAmB,OAAO;AAAA,QAC1B,iBAAiB,OAAO;AAAA,QACxB,kBAAkB,OAAO;AAAA,MAC3B;AAAA,MACA;AAAA,QACE,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,gBAAM,QAAQA,GAAE,sBAAsB,CAAC;AACvC,wBAAc,MAAM,OAAO,EAAE,EAAE;AAAA,QACjC;AAAA,QACA,SAAS,CAAC,MAAM;AACd,gBAAM,MAAM,EAAE,OAAO;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,EAAE,cAAc,IAAI,IAAI,qBAAqB;AAAA,IACjD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,EAAE,WAAW,YAAY,MAAM,IAAI,aAAa;AAAA,IACpD,WAAW,UAAU,IAAI,CAAC,GAAG,OAAO;AAAA,MAClC,cAAc,EAAE;AAAA,MAChB,MAAM,EAAE;AAAA,MACR,IAAI;AAAA,MACJ,OAAO,EAAE;AAAA,MACT,OAAO,EAAE;AAAA,MACT,UAAU,EAAE;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ,CAAC;AAAA,IACX,EAAE;AAAA,IACF,GAAG;AAAA,EACL,CAAC;AACD,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,cAAc,CAAC;AAAA,IACrB;AAAA,IACA;AAAA,IACA,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,cAAc;AAAA,MACZ,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,IACA,UAAU,CAAC,QAAQ,IAAI;AAAA,IACvB,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,gBAAgB,MAAM;AAC1B,UAAM,WAAW,OAAO,KAAK,YAAY,EAAE;AAAA,MACzC,CAAC,QAAQ,aAAa,GAAG;AAAA,IAC3B;AACA,SAAK;AAAA,MACH;AAAA,MACA,SAAS,IAAI,CAAC,SAAS;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,UAAU,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE;AAAA,MAC/C,EAAE;AAAA,MACF,EAAE,aAAa,MAAM,aAAa,KAAK;AAAA,IACzC;AACA,cAAU,kBAAkB,KAAK;AAAA,EACnC;AACA,QAAM,gBAAgB,CAAC,SAAS;AAC9B,UAAM,SAAS,kBAAkB,OAAO,CAAC,MAAM,EAAE,SAAS,IAAI;AAC9D,UAAM,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM;AACvD,UAAI,CAAC,IAAI;AACT,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AACL,SAAK,SAAS,aAAa,QAAQ,EAAE,aAAa,MAAM,aAAa,KAAK,CAAC;AAC3E,oBAAgB,GAAG;AAAA,EACrB;AACA,QAAM,iBAAiB,MAAM;AAC3B,SAAK,SAAS,aAAa,CAAC,GAAG,EAAE,aAAa,MAAM,aAAa,KAAK,CAAC;AACvE,oBAAgB,CAAC,CAAC;AAAA,EACpB;AACA,aAAuB,wBAAI,gBAAgB,MAAM,EAAE,MAAM,cAA0B;AAAA,IACjF;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,gBAAgB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cAClI,wBAAI,gBAAgB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cAC3J,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,iBAAiB,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QACxH,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,gBAAgB,MAAM,EAAE,WAAW,wBAAwB,cAA0B;AAAA,UACvG;AAAA,UACA;AAAA,YACE,WAAW;AAAA,cACT;AAAA,YACF;AAAA,YACA,IAAI;AAAA,YACJ,cAA0B,yBAAK,OAAO,EAAE,WAAW,8CAA8C,UAAU;AAAA,kBACzF,yBAAK,OAAO,EAAE,UAAU;AAAA,oBACtB,wBAAI,SAAS,EAAE,UAAUA,GAAE,sBAAsB,EAAE,CAAC;AAAA,oBACpD,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,0BAA0B,EAAE,CAAC;AAAA,cACtH,EAAE,CAAC;AAAA,kBACa,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,oBACrJ;AAAA,kBACd,KAAK;AAAA,kBACL;AAAA,oBACE,SAAS,KAAK;AAAA,oBACd,MAAM;AAAA,oBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,iCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,4BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,aAAa,EAAE,CAAC;AAAA,4BAC9C,wBAAI,KAAK,SAAS,EAAE,cAA0B,wBAAI,OAAO,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AAAA,4BACxE,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,sBAC3C,EAAE,CAAC;AAAA,oBACL;AAAA,kBACF;AAAA,gBACF;AAAA,oBACgB;AAAA,kBACd,KAAK;AAAA,kBACL;AAAA,oBACE,SAAS,KAAK;AAAA,oBACd,MAAM;AAAA,oBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,UAAU,KAAK,GAAG,MAAM,EAAE,MAAM;AAClD,iCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,4BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,iBAAiB,EAAE,CAAC;AAAA,4BAClD,wBAAI,KAAK,SAAS,EAAE,cAA0B,yBAAK,QAAQ,EAAE,GAAG,OAAO,eAAe,UAAU,UAAU;AAAA,8BACxG,wBAAI,OAAO,SAAS,EAAE,KAAK,cAA0B,wBAAI,OAAO,OAAO,CAAC,CAAC,EAAE,CAAC;AAAA,8BAC5E,wBAAI,OAAO,SAAS,EAAE,UAAU,YAAY,IAAI,CAAC,iBAA6B;AAAA,4BAC5F,OAAO;AAAA,4BACP;AAAA,8BACE,OAAO,SAAS;AAAA,8BAChB,UAAU,SAAS;AAAA,4BACrB;AAAA,4BACA,SAAS;AAAA,0BACX,CAAC,EAAE,CAAC;AAAA,wBACN,EAAE,CAAC,EAAE,CAAC;AAAA,4BACU,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,sBAC3C,EAAE,CAAC;AAAA,oBACL;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACU;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM;AAAA,kBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,+BAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,UAAU;AAAA,0BACxE,yBAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,4BACrE,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,4BACxD,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,0BAC5D;AAAA,0BACA;AAAA,4BACE,GAAG;AAAA,4BACH,SAAS;AAAA,4BACT,iBAAiB;AAAA,0BACnB;AAAA,wBACF,EAAE,CAAC;AAAA,sBACL,EAAE,CAAC;AAAA,0BACa,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,4BAA4B,EAAE,CAAC;AAAA,0BAC5D,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB;AAAA,gBACd,KAAK;AAAA,gBACL;AAAA,kBACE,SAAS,KAAK;AAAA,kBACd,MAAM;AAAA,kBACN,QAAQ,CAAC,EAAE,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,EAAE,MAAM;AACpD,+BAAuB,wBAAI,KAAK,MAAM,EAAE,cAA0B,yBAAK,OAAO,EAAE,UAAU;AAAA,0BACxE,yBAAK,OAAO,EAAE,WAAW,oCAAoC,UAAU;AAAA,4BACrE,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,4BAA4B,EAAE,CAAC;AAAA,4BAC7D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,0BAC5D;AAAA,0BACA;AAAA,4BACE,GAAG;AAAA,4BACH,SAAS;AAAA,4BACT,iBAAiB;AAAA,0BACnB;AAAA,wBACF,EAAE,CAAC;AAAA,sBACL,EAAE,CAAC;AAAA,0BACa,wBAAI,KAAK,MAAM,EAAE,UAAUA,GAAE,0BAA0B,EAAE,CAAC;AAAA,0BAC1D,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,oBAC3C,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,kBACgB,wBAAI,OAAO,EAAE,WAAW,gCAAgC,CAAC;AAAA,kBACzD,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,oBAC1D,yBAAK,OAAO,EAAE,UAAU;AAAA,sBACtB,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,sBAChG,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,gBACnH,EAAE,CAAC;AAAA,gBACH,kBAAkB,SAAS,SAAqB,yBAAK,OAAO,EAAE,WAAW,wBAAwB,UAAU;AAAA,kBACzG,kBAAkB,IAAI,CAAC,gBAA4B;AAAA,oBACjD;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA,UAAU;AAAA,oBACZ;AAAA,oBACA,QAAQ;AAAA,kBACV,CAAC;AAAA,sBACe;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,SAAS;AAAA,sBACT,MAAM;AAAA,sBACN,WAAW;AAAA,sBACX,SAAS;AAAA,sBACT,UAAUA,GAAE,kBAAkB;AAAA,oBAChC;AAAA,kBACF;AAAA,gBACF,EAAE,CAAC;AAAA,oBACa,yBAAK,mBAAmB,EAAE,IAAI,kBAAkB,UAAU;AAAA,sBACxD,wBAAI,OAAO,EAAE,WAAW,iCAAiC,cAA0B,wBAAI,kBAAkB,SAAS,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,sBAC3P,wBAAI,kBAAkB,SAAS,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,2CAA2C,UAAU;AAAA,wBACvI,wBAAI,kBAAkB,QAAQ,EAAE,cAA0B,wBAAI,kBAAkB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,WAAW,WAAW,UAAUA,GAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAAA,wBACjN,wBAAI,kBAAkB,MAAM,EAAE,WAAW,mBAAmB,cAA0B;AAAA,sBACpG;AAAA,sBACA;AAAA,wBACE;AAAA,wBACA;AAAA,wBACA;AAAA,wBACA,UAAU;AAAA,wBACV,SAAS;AAAA,0BACP,EAAE,KAAK,gBAAgB,OAAOA,GAAE,aAAa,EAAE;AAAA,0BAC/C,EAAE,KAAK,SAAS,OAAOA,GAAE,aAAa,EAAE;AAAA,wBAC1C;AAAA,wBACA,YAAY;AAAA,wBACZ,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,aAAa;AAAA,wBACb,QAAQ;AAAA,sBACV;AAAA,oBACF,EAAE,CAAC;AAAA,wBACa,wBAAI,kBAAkB,QAAQ,EAAE,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,0BACpI,wBAAI,kBAAkB,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,SAAS,aAAa,MAAM,SAAS,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,0BAC7J;AAAA,wBACd;AAAA,wBACA;AAAA,0BACE,MAAM;AAAA,0BACN,MAAM;AAAA,0BACN,SAAS;AAAA,0BACT,UAAUA,GAAE,cAAc;AAAA,wBAC5B;AAAA,sBACF;AAAA,oBACF,EAAE,CAAC,EAAE,CAAC;AAAA,kBACR,EAAE,CAAC,EAAE,CAAC;AAAA,gBACR,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,kBACa,wBAAI,OAAO,EAAE,WAAW,gCAAgC,CAAC;AAAA,kBACzD,yBAAK,OAAO,EAAE,WAAW,yBAAyB,UAAU;AAAA,oBAC1D,yBAAK,OAAO,EAAE,UAAU;AAAA,sBACtB,wBAAI,MAAM,EAAE,MAAM,SAAS,SAAS,WAAW,QAAQ,QAAQ,UAAUA,GAAE,kBAAkB,EAAE,CAAC;AAAA,sBAChG,wBAAI,MAAM,EAAE,MAAM,SAAS,WAAW,qBAAqB,UAAUA,GAAE,uBAAuB,EAAE,CAAC;AAAA,gBACnH,EAAE,CAAC;AAAA,oBACa,wBAAI,OAAO,EAAE,WAAW,yCAAyC,cAA0B;AAAA,kBACzG,KAAK;AAAA,kBACL;AAAA,oBACE,SAAS,KAAK;AAAA,oBACd,MAAM;AAAA,oBACN,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,iCAAuB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,4BACjC,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,yBAAyB,EAAE,CAAC;AAAA,4BAC1D,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,0BAC5D;AAAA,0BACA;AAAA,4BACE,SAAS,iBAAiB,IAAI,CAAC,QAAQ;AAAA,8BACrC,OAAO,eAAe,GAAG,EAAE;AAAA,8BAC3B,OAAO,GAAG;AAAA,4BACZ,EAAE;AAAA,4BACF,GAAG;AAAA,0BACL;AAAA,wBACF,EAAE,CAAC;AAAA,4BACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,sBAC3C,EAAE,CAAC;AAAA,oBACL;AAAA,kBACF;AAAA,gBACF,EAAE,CAAC;AAAA,cACL,EAAE,CAAC;AAAA,YACL,EAAE,CAAC;AAAA,UACL;AAAA,QACF,EAAE,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,mBAAmB;AACtC,IAAI,aAAa,MAAM;AACrB,QAAM,OAAO,uBAAuB;AACpC,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,QAAQ;AAAA,QACnB,IAAI;AAAA,QACJ,QAAQ,CAAC,EAAE,MAAM,MAAM;AACrB,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,MAAM,0BAA0B,IAAI,kBAAkB,MAAM,yBAAyB;AAAA,cAC9F,iBAAiB,CAAC,UAAU,MAAM,0BAA0B,CAAC,CAAC,KAAK;AAAA,YACrE;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,gBAAM,gBAAgB,CAAC,IAAI,aAAa;AACxC,qBAAuB;AAAA,YACrB;AAAA,YACA;AAAA,cACE,SAAS,IAAI,cAAc,KAAK;AAAA,cAChC,UAAU;AAAA,cACV,iBAAiB,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC,KAAK;AAAA,cACtD,SAAS,CAAC,MAAM;AACd,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,MACD,GAAG;AAAA,IACL;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AACF;AACA,IAAI,aAAa,CAAC;AAAA,EAChB;AAAA,EACA;AACF,MAAM;AACJ,aAAuB,yBAAK,OAAO,EAAE,WAAW,gIAAgI,UAAU;AAAA,QACxK,wBAAI,OAAO,EAAE,WAAW,0FAA0F,UAAU,QAAQ,KAAK,CAAC;AAAA,QAC1I;AAAA,MACd;AAAA,MACA;AAAA,QACE,MAAM;AAAA,QACN,SAAS,MAAM,SAAS,QAAQ,IAAI;AAAA,QACpC,WAAW;AAAA,QACX,cAA0B,wBAAI,WAAW,EAAE,WAAW,mBAAmB,CAAC;AAAA,MAC5E;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,eAAe,MAAM;AACvB,QAAM,EAAE,OAAO,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AACjE,QAAM,oBAAmB,+BAAO,yBAAwB,CAAC,GAAG;AAAA,IAC1D,CAAC,MAAM,WAAW,EAAE,cAAc,YAAY,CAAC;AAAA,EACjD;AACA,QAAM,EAAE,mBAAmB,mBAAmB,CAAC,EAAE,IAAI,oBAAoB;AAAA,IACvE,YAAY;AAAA,EACd,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,KAAK,iBAAiB,EAAE,UAAU,CAAC,aAAa,aAAyB,oBAAAA;AAAA,IAC9F;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsx2"]}