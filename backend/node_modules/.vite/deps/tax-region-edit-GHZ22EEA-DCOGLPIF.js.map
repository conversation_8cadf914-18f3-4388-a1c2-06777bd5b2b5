{"version": 3, "sources": ["../../@medusajs/dashboard/dist/tax-region-edit-GHZ22EEA.mjs"], "sourcesContent": ["import {\n  formatProvider\n} from \"./chunk-IR5DHEKS.mjs\";\nimport {\n  useComboboxData\n} from \"./chunk-HFB3OQXI.mjs\";\nimport {\n  Combobox\n} from \"./chunk-GZBFGV7Y.mjs\";\nimport \"./chunk-IUCDCPJU.mjs\";\nimport {\n  KeyboundForm\n} from \"./chunk-6HTZNHPT.mjs\";\nimport {\n  RouteDrawer,\n  useRouteModal\n} from \"./chunk-4TC5YS65.mjs\";\nimport {\n  Form\n} from \"./chunk-OBQI23QM.mjs\";\nimport \"./chunk-Z5UDPQIH.mjs\";\nimport \"./chunk-KOSCMAIC.mjs\";\nimport \"./chunk-X6DSNTTX.mjs\";\nimport {\n  useTaxRegion,\n  useUpdateTaxRegion\n} from \"./chunk-I6E6CALJ.mjs\";\nimport \"./chunk-B4GODIOW.mjs\";\nimport \"./chunk-F6IJV2I2.mjs\";\nimport \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-ENV6YVOM.mjs\";\nimport \"./chunk-PIR2H25N.mjs\";\nimport \"./chunk-RLY2SL5E.mjs\";\nimport \"./chunk-C5LYZZZ5.mjs\";\nimport \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FO3VP56P.mjs\";\nimport \"./chunk-YS65UGPC.mjs\";\nimport \"./chunk-F6PXCY3N.mjs\";\nimport \"./chunk-3OHH43G6.mjs\";\nimport \"./chunk-G2H6MAK7.mjs\";\nimport \"./chunk-GRT22PE5.mjs\";\nimport \"./chunk-32IQRUVY.mjs\";\nimport \"./chunk-FNYASI54.mjs\";\nimport \"./chunk-FVC7M755.mjs\";\nimport \"./chunk-ZJ3OFMHB.mjs\";\nimport \"./chunk-PNU5HPGY.mjs\";\nimport \"./chunk-V2LANK5S.mjs\";\nimport \"./chunk-QZ6PT4QV.mjs\";\nimport \"./chunk-QL4XKIVL.mjs\";\nimport \"./chunk-6I62UDJA.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport {\n  sdk\n} from \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/tax-regions/tax-region-edit/tax-region-edit.tsx\nimport { Heading } from \"@medusajs/ui\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\nimport { useParams } from \"react-router-dom\";\n\n// src/routes/tax-regions/tax-region-edit/components/tax-region-edit/tax-region-edit-form.tsx\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Button, toast } from \"@medusajs/ui\";\nimport { useForm } from \"react-hook-form\";\nimport { useTranslation } from \"react-i18next\";\nimport { z } from \"zod\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar TaxRegionEditSchema = z.object({\n  provider_id: z.string().min(1)\n});\nvar TaxRegionEditForm = ({ taxRegion }) => {\n  const { t } = useTranslation();\n  const { handleSuccess } = useRouteModal();\n  const taxProviders = useComboboxData({\n    queryKey: [\"tax_providers\"],\n    queryFn: (params) => sdk.admin.taxProvider.list(params),\n    getOptions: (data) => data.tax_providers.map((provider) => ({\n      label: formatProvider(provider.id),\n      value: provider.id\n    }))\n  });\n  const form = useForm({\n    defaultValues: {\n      provider_id: taxRegion.provider_id\n    },\n    resolver: zodResolver(TaxRegionEditSchema)\n  });\n  const { mutateAsync, isPending } = useUpdateTaxRegion(taxRegion.id);\n  const handleSubmit = form.handleSubmit(async (values) => {\n    await mutateAsync(\n      {\n        provider_id: values.provider_id\n      },\n      {\n        onSuccess: () => {\n          toast.success(t(\"taxRegions.edit.successToast\"));\n          handleSuccess();\n        },\n        onError: (error) => {\n          toast.error(error.message);\n        }\n      }\n    );\n  });\n  return /* @__PURE__ */ jsx(RouteDrawer.Form, { form, children: /* @__PURE__ */ jsxs(\n    KeyboundForm,\n    {\n      className: \"flex flex-1 flex-col overflow-hidden\",\n      onSubmit: handleSubmit,\n      children: [\n        /* @__PURE__ */ jsx(RouteDrawer.Body, { className: \"flex flex-1 flex-col gap-y-6 overflow-auto\", children: /* @__PURE__ */ jsx(\"div\", { className: \"flex flex-col gap-y-4\", children: /* @__PURE__ */ jsx(\n          Form.Field,\n          {\n            control: form.control,\n            name: \"provider_id\",\n            render: ({ field }) => /* @__PURE__ */ jsxs(Form.Item, { children: [\n              /* @__PURE__ */ jsx(Form.Label, { children: t(\"taxRegions.fields.taxProvider\") }),\n              /* @__PURE__ */ jsx(Form.Control, { children: /* @__PURE__ */ jsx(\n                Combobox,\n                {\n                  ...field,\n                  options: taxProviders.options,\n                  searchValue: taxProviders.searchValue,\n                  onSearchValueChange: taxProviders.onSearchValueChange,\n                  fetchNextPage: taxProviders.fetchNextPage\n                }\n              ) }),\n              /* @__PURE__ */ jsx(Form.ErrorMessage, {})\n            ] })\n          }\n        ) }) }),\n        /* @__PURE__ */ jsx(RouteDrawer.Footer, { className: \"shrink-0\", children: /* @__PURE__ */ jsxs(\"div\", { className: \"flex items-center justify-end gap-x-2\", children: [\n          /* @__PURE__ */ jsx(RouteDrawer.Close, { asChild: true, children: /* @__PURE__ */ jsx(Button, { size: \"small\", variant: \"secondary\", children: t(\"actions.cancel\") }) }),\n          /* @__PURE__ */ jsx(Button, { size: \"small\", type: \"submit\", isLoading: isPending, children: t(\"actions.save\") })\n        ] }) })\n      ]\n    }\n  ) });\n};\n\n// src/routes/tax-regions/tax-region-edit/tax-region-edit.tsx\nimport { jsx as jsx2, jsxs as jsxs2 } from \"react/jsx-runtime\";\nvar TaxRegionEdit = () => {\n  const { t } = useTranslation2();\n  const { id } = useParams();\n  const { tax_region, isPending, isError, error } = useTaxRegion(id);\n  const ready = !isPending && !!tax_region;\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs2(RouteDrawer, { children: [\n    /* @__PURE__ */ jsxs2(RouteDrawer.Header, { children: [\n      /* @__PURE__ */ jsx2(RouteDrawer.Title, { asChild: true, children: /* @__PURE__ */ jsx2(Heading, { children: t(\"taxRegions.edit.header\") }) }),\n      /* @__PURE__ */ jsx2(RouteDrawer.Description, { className: \"sr-only\", children: t(\"taxRegions.edit.hint\") })\n    ] }),\n    ready && /* @__PURE__ */ jsx2(TaxRegionEditForm, { taxRegion: tax_region })\n  ] });\n};\nexport {\n  TaxRegionEdit as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA,yBAA0B;AA2E1B,IAAAA,sBAA2C;AA1E3C,IAAI,sBAAsB,EAAE,OAAO;AAAA,EACjC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC;AAC/B,CAAC;AACD,IAAI,oBAAoB,CAAC,EAAE,UAAU,MAAM;AACzC,QAAM,EAAE,GAAAC,GAAE,IAAI,eAAe;AAC7B,QAAM,EAAE,cAAc,IAAI,cAAc;AACxC,QAAM,eAAe,gBAAgB;AAAA,IACnC,UAAU,CAAC,eAAe;AAAA,IAC1B,SAAS,CAAC,WAAW,IAAI,MAAM,YAAY,KAAK,MAAM;AAAA,IACtD,YAAY,CAAC,SAAS,KAAK,cAAc,IAAI,CAAC,cAAc;AAAA,MAC1D,OAAO,eAAe,SAAS,EAAE;AAAA,MACjC,OAAO,SAAS;AAAA,IAClB,EAAE;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,QAAQ;AAAA,IACnB,eAAe;AAAA,MACb,aAAa,UAAU;AAAA,IACzB;AAAA,IACA,UAAU,EAAY,mBAAmB;AAAA,EAC3C,CAAC;AACD,QAAM,EAAE,aAAa,UAAU,IAAI,mBAAmB,UAAU,EAAE;AAClE,QAAM,eAAe,KAAK,aAAa,OAAO,WAAW;AACvD,UAAM;AAAA,MACJ;AAAA,QACE,aAAa,OAAO;AAAA,MACtB;AAAA,MACA;AAAA,QACE,WAAW,MAAM;AACf,gBAAM,QAAQA,GAAE,8BAA8B,CAAC;AAC/C,wBAAc;AAAA,QAChB;AAAA,QACA,SAAS,CAAC,UAAU;AAClB,gBAAM,MAAM,MAAM,OAAO;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAuB,wBAAI,YAAY,MAAM,EAAE,MAAM,cAA0B;AAAA,IAC7E;AAAA,IACA;AAAA,MACE,WAAW;AAAA,MACX,UAAU;AAAA,MACV,UAAU;AAAA,YACQ,wBAAI,YAAY,MAAM,EAAE,WAAW,8CAA8C,cAA0B,wBAAI,OAAO,EAAE,WAAW,yBAAyB,cAA0B;AAAA,UACpM,KAAK;AAAA,UACL;AAAA,YACE,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,YACN,QAAQ,CAAC,EAAE,MAAM,UAAsB,yBAAK,KAAK,MAAM,EAAE,UAAU;AAAA,kBACjD,wBAAI,KAAK,OAAO,EAAE,UAAUA,GAAE,+BAA+B,EAAE,CAAC;AAAA,kBAChE,wBAAI,KAAK,SAAS,EAAE,cAA0B;AAAA,gBAC5D;AAAA,gBACA;AAAA,kBACE,GAAG;AAAA,kBACH,SAAS,aAAa;AAAA,kBACtB,aAAa,aAAa;AAAA,kBAC1B,qBAAqB,aAAa;AAAA,kBAClC,eAAe,aAAa;AAAA,gBAC9B;AAAA,cACF,EAAE,CAAC;AAAA,kBACa,wBAAI,KAAK,cAAc,CAAC,CAAC;AAAA,YAC3C,EAAE,CAAC;AAAA,UACL;AAAA,QACF,EAAE,CAAC,EAAE,CAAC;AAAA,YACU,wBAAI,YAAY,QAAQ,EAAE,WAAW,YAAY,cAA0B,yBAAK,OAAO,EAAE,WAAW,yCAAyC,UAAU;AAAA,cACrJ,wBAAI,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,wBAAI,QAAQ,EAAE,MAAM,SAAS,SAAS,aAAa,UAAUA,GAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;AAAA,cACvJ,wBAAI,QAAQ,EAAE,MAAM,SAAS,MAAM,UAAU,WAAW,WAAW,UAAUA,GAAE,cAAc,EAAE,CAAC;AAAA,QAClH,EAAE,CAAC,EAAE,CAAC;AAAA,MACR;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,GAAAA,GAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,GAAG,IAAI,UAAU;AACzB,QAAM,EAAE,YAAY,WAAW,SAAS,MAAM,IAAI,aAAa,EAAE;AACjE,QAAM,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC9B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,oBAAAC,MAAM,aAAa,EAAE,UAAU;AAAA,QACpC,oBAAAA,MAAM,YAAY,QAAQ,EAAE,UAAU;AAAA,UACpC,oBAAAC,KAAK,YAAY,OAAO,EAAE,SAAS,MAAM,cAA0B,oBAAAA,KAAK,SAAS,EAAE,UAAUF,GAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;AAAA,UAC7H,oBAAAE,KAAK,YAAY,aAAa,EAAE,WAAW,WAAW,UAAUF,GAAE,sBAAsB,EAAE,CAAC;AAAA,IAC7G,EAAE,CAAC;AAAA,IACH,aAAyB,oBAAAE,KAAK,mBAAmB,EAAE,WAAW,WAAW,CAAC;AAAA,EAC5E,EAAE,CAAC;AACL;", "names": ["import_jsx_runtime", "t", "jsxs2", "jsx2"]}