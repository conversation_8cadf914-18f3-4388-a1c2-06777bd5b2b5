import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteFocusModal,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import {
  useCreateShippingProfile
} from "./chunk-R73OU4H7.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  Text,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/shipping-profile-create-KRMI6NUI.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var CreateShippingOptionsSchema = objectType({
  name: stringType().min(1),
  type: stringType().min(1)
});
function CreateShippingProfileForm() {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: "",
      type: ""
    },
    resolver: t(CreateShippingOptionsSchema)
  });
  const { mutateAsync, isPending } = useCreateShippingProfile();
  const handleSubmit = form.handleSubmit(async (values) => {
    await mutateAsync(
      {
        name: values.name,
        type: values.type
      },
      {
        onSuccess: ({ shipping_profile }) => {
          toast.success(
            t2("shippingProfile.create.successToast", {
              name: shipping_profile.name
            })
          );
          handleSuccess(
            `/settings/locations/shipping-profiles/${shipping_profile.id}`
          );
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteFocusModal.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex h-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Header, {}),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Body, { className: "flex flex-1 flex-col overflow-hidden", children: (0, import_jsx_runtime.jsx)("div", { className: "flex flex-1 flex-col items-center overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "mx-auto flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16", children: [
          (0, import_jsx_runtime.jsxs)("div", { children: [
            (0, import_jsx_runtime.jsx)(Heading, { className: "capitalize", children: t2("shippingProfile.create.header") }),
            (0, import_jsx_runtime.jsx)(Text, { size: "small", className: "text-ui-fg-subtle", children: t2("shippingProfile.create.hint") })
          ] }),
          (0, import_jsx_runtime.jsxs)("div", { className: "grid grid-cols-2 gap-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "name",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "type",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { tooltip: t2("shippingProfile.tooltip.type"), children: t2("fields.type") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }) }) }),
        (0, import_jsx_runtime.jsx)(RouteFocusModal.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteFocusModal.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
}
function ShippingProfileCreate() {
  return (0, import_jsx_runtime2.jsx)(RouteFocusModal, { children: (0, import_jsx_runtime2.jsx)(CreateShippingProfileForm, {}) });
}
export {
  ShippingProfileCreate as Component
};
//# sourceMappingURL=shipping-profile-create-KRMI6NUI-I7Y5RJU5.js.map
