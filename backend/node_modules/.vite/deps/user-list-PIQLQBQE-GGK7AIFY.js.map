{"version": 3, "sources": ["../../@medusajs/dashboard/dist/user-list-PIQLQBQE.mjs"], "sourcesContent": ["import {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useDataTableDateColumns\n} from \"./chunk-2D44OO7A.mjs\";\nimport {\n  DataTable,\n  useDataTableDateFilters\n} from \"./chunk-WCEBGSIU.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-3NJTXRIY.mjs\";\nimport \"./chunk-OC7BQLYI.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport {\n  useUsers\n} from \"./chunk-2ZKVRTBW.mjs\";\nimport \"./chunk-FXYH54JP.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/users/user-list/components/user-list-table/user-list-table.tsx\nimport { Container, createDataTableColumnHelper } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { useNavigate } from \"react-router-dom\";\nimport { PencilSquare } from \"@medusajs/icons\";\nimport { jsx } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar UserListTable = () => {\n  const { q, order, offset } = useQueryParams([\"q\", \"order\", \"offset\"]);\n  const { users, count, isPending, isError, error } = useUsers(\n    {\n      q,\n      order,\n      offset: offset ? parseInt(offset) : 0,\n      limit: PAGE_SIZE\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useColumns();\n  const filters = useFilters();\n  const { t } = useTranslation();\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsx(Container, { className: \"divide-y p-0\", children: /* @__PURE__ */ jsx(\n    DataTable,\n    {\n      data: users,\n      columns,\n      filters,\n      getRowId: (row) => row.id,\n      rowCount: count,\n      pageSize: PAGE_SIZE,\n      heading: t(\"users.domain\"),\n      rowHref: (row) => `${row.id}`,\n      isLoading: isPending,\n      action: {\n        label: t(\"users.invite\"),\n        to: \"invite\"\n      },\n      emptyState: {\n        empty: {\n          heading: t(\"users.list.empty.heading\"),\n          description: t(\"users.list.empty.description\")\n        },\n        filtered: {\n          heading: t(\"users.list.filtered.heading\"),\n          description: t(\"users.list.filtered.description\")\n        }\n      }\n    }\n  ) });\n};\nvar columnHelper = createDataTableColumnHelper();\nvar useColumns = () => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const dateColumns = useDataTableDateColumns();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"email\", {\n        header: t(\"fields.email\"),\n        cell: ({ row }) => {\n          return row.original.email;\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      columnHelper.accessor(\"first_name\", {\n        header: t(\"fields.firstName\"),\n        cell: ({ row }) => {\n          return row.original.first_name || \"-\";\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      columnHelper.accessor(\"last_name\", {\n        header: t(\"fields.lastName\"),\n        cell: ({ row }) => {\n          return row.original.last_name || \"-\";\n        },\n        enableSorting: true,\n        sortAscLabel: t(\"filters.sorting.alphabeticallyAsc\"),\n        sortDescLabel: t(\"filters.sorting.alphabeticallyDesc\")\n      }),\n      ...dateColumns,\n      columnHelper.action({\n        actions: [\n          {\n            label: t(\"actions.edit\"),\n            icon: /* @__PURE__ */ jsx(PencilSquare, {}),\n            onClick: (ctx) => {\n              navigate(`${ctx.row.original.id}/edit`);\n            }\n          }\n        ]\n      })\n    ],\n    [t, navigate, dateColumns]\n  );\n};\nvar useFilters = () => {\n  const dateFilters = useDataTableDateFilters();\n  return useMemo(() => {\n    return dateFilters;\n  }, [dateFilters]);\n};\n\n// src/routes/users/user-list/user-list.tsx\nimport { jsx as jsx2 } from \"react/jsx-runtime\";\nvar UserList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx2(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"user.list.after\"),\n        before: getWidgets(\"user.list.before\")\n      },\n      children: /* @__PURE__ */ jsx2(UserListTable, {})\n    }\n  );\n};\nexport {\n  UserList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,mBAAwB;AAIxB,yBAAoB;AA4GpB,IAAAA,sBAA4B;AA3G5B,IAAI,YAAY;AAChB,IAAI,gBAAgB,MAAM;AACxB,QAAM,EAAE,GAAG,OAAO,OAAO,IAAI,eAAe,CAAC,KAAK,SAAS,QAAQ,CAAC;AACpE,QAAM,EAAE,OAAO,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAClD;AAAA,MACE;AAAA,MACA;AAAA,MACA,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,MACpC,OAAO;AAAA,IACT;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,WAAW;AAC3B,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,wBAAI,WAAW,EAAE,WAAW,gBAAgB,cAA0B;AAAA,IAC3F;AAAA,IACA;AAAA,MACE,MAAM;AAAA,MACN;AAAA,MACA;AAAA,MACA,UAAU,CAAC,QAAQ,IAAI;AAAA,MACvB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,EAAE,cAAc;AAAA,MACzB,SAAS,CAAC,QAAQ,GAAG,IAAI,EAAE;AAAA,MAC3B,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,OAAO,EAAE,cAAc;AAAA,QACvB,IAAI;AAAA,MACN;AAAA,MACA,YAAY;AAAA,QACV,OAAO;AAAA,UACL,SAAS,EAAE,0BAA0B;AAAA,UACrC,aAAa,EAAE,8BAA8B;AAAA,QAC/C;AAAA,QACA,UAAU;AAAA,UACR,SAAS,EAAE,6BAA6B;AAAA,UACxC,aAAa,EAAE,iCAAiC;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AACA,IAAI,eAAe,4BAA4B;AAC/C,IAAI,aAAa,MAAM;AACrB,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,QAAM,WAAW,YAAY;AAC7B,QAAM,cAAc,wBAAwB;AAC5C,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,cAAc;AAAA,QACxB,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,iBAAO,IAAI,SAAS;AAAA,QACtB;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,aAAa,SAAS,cAAc;AAAA,QAClC,QAAQ,EAAE,kBAAkB;AAAA,QAC5B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,iBAAO,IAAI,SAAS,cAAc;AAAA,QACpC;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,EAAE,iBAAiB;AAAA,QAC3B,MAAM,CAAC,EAAE,IAAI,MAAM;AACjB,iBAAO,IAAI,SAAS,aAAa;AAAA,QACnC;AAAA,QACA,eAAe;AAAA,QACf,cAAc,EAAE,mCAAmC;AAAA,QACnD,eAAe,EAAE,oCAAoC;AAAA,MACvD,CAAC;AAAA,MACD,GAAG;AAAA,MACH,aAAa,OAAO;AAAA,QAClB,SAAS;AAAA,UACP;AAAA,YACE,OAAO,EAAE,cAAc;AAAA,YACvB,UAAsB,wBAAI,cAAc,CAAC,CAAC;AAAA,YAC1C,SAAS,CAAC,QAAQ;AAChB,uBAAS,GAAG,IAAI,IAAI,SAAS,EAAE,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,GAAG,UAAU,WAAW;AAAA,EAC3B;AACF;AACA,IAAI,aAAa,MAAM;AACrB,QAAM,cAAc,wBAAwB;AAC5C,aAAO,sBAAQ,MAAM;AACnB,WAAO;AAAA,EACT,GAAG,CAAC,WAAW,CAAC;AAClB;AAIA,IAAI,WAAW,MAAM;AACnB,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,iBAAiB;AAAA,QACnC,QAAQ,WAAW,kBAAkB;AAAA,MACvC;AAAA,MACA,cAA0B,oBAAAA,KAAK,eAAe,CAAC,CAAC;AAAA,IAClD;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2"]}