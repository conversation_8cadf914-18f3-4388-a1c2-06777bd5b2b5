import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import {
  useDataTableDateColumns
} from "./chunk-ZMX3GMKA.js";
import {
  DataTable,
  useDataTableDateFilters
} from "./chunk-CHHMXAMR.js";
import {
  useQueryParams
} from "./chunk-32T72GVU.js";
import "./chunk-WP4SM64O.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import {
  useUsers
} from "./chunk-Y6WFHOFY.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useNavigate
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  PencilSquare,
  createDataTableColumnHelper
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/user-list-PIQLQBQE.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var PAGE_SIZE = 20;
var UserListTable = () => {
  const { q, order, offset } = useQueryParams(["q", "order", "offset"]);
  const { users, count, isPending, isError, error } = useUsers(
    {
      q,
      order,
      offset: offset ? parseInt(offset) : 0,
      limit: PAGE_SIZE
    },
    {
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useFilters();
  const { t } = useTranslation();
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsx)(Container, { className: "divide-y p-0", children: (0, import_jsx_runtime.jsx)(
    DataTable,
    {
      data: users,
      columns,
      filters,
      getRowId: (row) => row.id,
      rowCount: count,
      pageSize: PAGE_SIZE,
      heading: t("users.domain"),
      rowHref: (row) => `${row.id}`,
      isLoading: isPending,
      action: {
        label: t("users.invite"),
        to: "invite"
      },
      emptyState: {
        empty: {
          heading: t("users.list.empty.heading"),
          description: t("users.list.empty.description")
        },
        filtered: {
          heading: t("users.list.filtered.heading"),
          description: t("users.list.filtered.description")
        }
      }
    }
  ) });
};
var columnHelper = createDataTableColumnHelper();
var useColumns = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dateColumns = useDataTableDateColumns();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.accessor("email", {
        header: t("fields.email"),
        cell: ({ row }) => {
          return row.original.email;
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("first_name", {
        header: t("fields.firstName"),
        cell: ({ row }) => {
          return row.original.first_name || "-";
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      columnHelper.accessor("last_name", {
        header: t("fields.lastName"),
        cell: ({ row }) => {
          return row.original.last_name || "-";
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc")
      }),
      ...dateColumns,
      columnHelper.action({
        actions: [
          {
            label: t("actions.edit"),
            icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
            onClick: (ctx) => {
              navigate(`${ctx.row.original.id}/edit`);
            }
          }
        ]
      })
    ],
    [t, navigate, dateColumns]
  );
};
var useFilters = () => {
  const dateFilters = useDataTableDateFilters();
  return (0, import_react.useMemo)(() => {
    return dateFilters;
  }, [dateFilters]);
};
var UserList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("user.list.after"),
        before: getWidgets("user.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(UserListTable, {})
    }
  );
};
export {
  UserList as Component
};
//# sourceMappingURL=user-list-PIQLQBQE-GGK7AIFY.js.map
