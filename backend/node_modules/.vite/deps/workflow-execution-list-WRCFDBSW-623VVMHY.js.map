{"version": 3, "sources": ["../../@medusajs/dashboard/dist/workflow-execution-list-WRCFDBSW.mjs"], "sourcesContent": ["import {\n  getTransactionState,\n  getTransactionStateColor\n} from \"./chunk-RPAL6FHW.mjs\";\nimport {\n  StatusCell\n} from \"./chunk-ADOCJB6L.mjs\";\nimport \"./chunk-LQTHYS2Z.mjs\";\nimport {\n  _DataTable,\n  useDataTable\n} from \"./chunk-B2JT2FOA.mjs\";\nimport \"./chunk-YEDAFXMB.mjs\";\nimport \"./chunk-AOFGTNG6.mjs\";\nimport \"./chunk-EMIHDNB7.mjs\";\nimport \"./chunk-GJUPECDU.mjs\";\nimport \"./chunk-M3VFKDXJ.mjs\";\nimport {\n  SingleColumnPage\n} from \"./chunk-2RQLKDBF.mjs\";\nimport {\n  useQueryParams\n} from \"./chunk-C76H5USB.mjs\";\nimport \"./chunk-PFKKVLZX.mjs\";\nimport {\n  useExtension\n} from \"./chunk-C5P5PL3E.mjs\";\nimport \"./chunk-LPEUYMRK.mjs\";\nimport \"./chunk-67ORSRVT.mjs\";\nimport {\n  useWorkflowExecutions\n} from \"./chunk-QTCZFYFH.mjs\";\nimport \"./chunk-774WSTCC.mjs\";\nimport \"./chunk-DEQUVHHE.mjs\";\nimport \"./chunk-RPUOO7AV.mjs\";\n\n// src/routes/workflow-executions/workflow-execution-list/components/workflow-execution-list-table/workflow-execution-list-table.tsx\nimport { Container, Heading, Text } from \"@medusajs/ui\";\nimport { keepPreviousData } from \"@tanstack/react-query\";\nimport { useTranslation as useTranslation2 } from \"react-i18next\";\n\n// src/routes/workflow-executions/workflow-execution-list/components/workflow-execution-list-table/use-workflow-execution-table-columns.tsx\nimport { Badge } from \"@medusajs/ui\";\nimport { createColumnHelper } from \"@tanstack/react-table\";\nimport { useMemo } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsx } from \"react/jsx-runtime\";\nvar columnHelper = createColumnHelper();\nvar useWorkflowExecutionTableColumns = () => {\n  const { t } = useTranslation();\n  return useMemo(\n    () => [\n      columnHelper.accessor(\"transaction_id\", {\n        header: t(\"workflowExecutions.transactionIdLabel\"),\n        cell: ({ getValue }) => /* @__PURE__ */ jsx(Badge, { size: \"2xsmall\", children: getValue() })\n      }),\n      columnHelper.accessor(\"state\", {\n        header: t(\"fields.state\"),\n        cell: ({ getValue }) => {\n          const state = getValue();\n          const color = getTransactionStateColor(state);\n          const translatedState = getTransactionState(t, state);\n          return /* @__PURE__ */ jsx(StatusCell, { color, children: /* @__PURE__ */ jsx(\"span\", { className: \"capitalize\", children: translatedState }) });\n        }\n      }),\n      columnHelper.accessor(\"execution\", {\n        header: t(\"workflowExecutions.progressLabel\"),\n        cell: ({ getValue }) => {\n          const steps = getValue()?.steps;\n          if (!steps) {\n            return \"0 of 0 steps\";\n          }\n          const actionableSteps = Object.values(steps).filter(\n            (step) => step.id !== ROOT_PREFIX\n          );\n          const completedSteps = actionableSteps.filter(\n            (step) => step.invoke.state === \"done\" /* DONE */\n          );\n          return t(\"workflowExecutions.stepsCompletedLabel\", {\n            completed: completedSteps.length,\n            count: actionableSteps.length\n          });\n        }\n      })\n    ],\n    [t]\n  );\n};\nvar ROOT_PREFIX = \"_root\";\n\n// src/routes/workflow-executions/workflow-execution-list/components/workflow-execution-list-table/use-workflow-execution-table-query.tsx\nvar useWorkflowExecutionTableQuery = ({\n  pageSize = 20,\n  prefix\n}) => {\n  const raw = useQueryParams([\"q\", \"offset\"], prefix);\n  const { offset, ...rest } = raw;\n  const searchParams = {\n    limit: pageSize,\n    offset: offset ? parseInt(offset) : 0,\n    ...rest\n  };\n  return {\n    searchParams,\n    raw\n  };\n};\n\n// src/routes/workflow-executions/workflow-execution-list/components/workflow-execution-list-table/workflow-execution-list-table.tsx\nimport { jsx as jsx2, jsxs } from \"react/jsx-runtime\";\nvar PAGE_SIZE = 20;\nvar WorkflowExecutionListTable = () => {\n  const { t } = useTranslation2();\n  const { searchParams, raw } = useWorkflowExecutionTableQuery({\n    pageSize: PAGE_SIZE\n  });\n  const { workflow_executions, count, isLoading, isError, error } = useWorkflowExecutions(\n    {\n      ...searchParams\n    },\n    {\n      placeholderData: keepPreviousData\n    }\n  );\n  const columns = useWorkflowExecutionTableColumns();\n  const { table } = useDataTable({\n    data: workflow_executions || [],\n    columns,\n    count,\n    pageSize: PAGE_SIZE,\n    enablePagination: true,\n    getRowId: (row) => row.id\n  });\n  if (isError) {\n    throw error;\n  }\n  return /* @__PURE__ */ jsxs(Container, { className: \"divide-y p-0\", children: [\n    /* @__PURE__ */ jsx2(\"div\", { className: \"flex items-center justify-between px-6 py-4\", children: /* @__PURE__ */ jsxs(\"div\", { children: [\n      /* @__PURE__ */ jsx2(Heading, { children: t(\"workflowExecutions.domain\") }),\n      /* @__PURE__ */ jsx2(Text, { className: \"text-ui-fg-subtle\", size: \"small\", children: t(`workflowExecutions.subtitle`) })\n    ] }) }),\n    /* @__PURE__ */ jsx2(\n      _DataTable,\n      {\n        table,\n        columns,\n        count,\n        isLoading,\n        pageSize: PAGE_SIZE,\n        navigateTo: (row) => `${row.id}`,\n        search: true,\n        pagination: true,\n        queryObject: raw,\n        noRecords: {\n          message: t(\"workflowExecutions.list.noRecordsMessage\")\n        }\n      }\n    )\n  ] });\n};\n\n// src/routes/workflow-executions/workflow-execution-list/workflow-execution-list.tsx\nimport { jsx as jsx3 } from \"react/jsx-runtime\";\nvar WorkflowExcecutionList = () => {\n  const { getWidgets } = useExtension();\n  return /* @__PURE__ */ jsx3(\n    SingleColumnPage,\n    {\n      widgets: {\n        after: getWidgets(\"workflow.list.after\"),\n        before: getWidgets(\"workflow.list.before\")\n      },\n      hasOutlet: false,\n      children: /* @__PURE__ */ jsx3(WorkflowExecutionListTable, {})\n    }\n  );\n};\nexport {\n  WorkflowExcecutionList as Component\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,mBAAwB;AAExB,yBAAoB;AA+DpB,IAAAA,sBAAkC;AAqDlC,IAAAA,sBAA4B;AAnH5B,IAAI,eAAe,mBAAmB;AACtC,IAAI,mCAAmC,MAAM;AAC3C,QAAM,EAAE,EAAE,IAAI,eAAe;AAC7B,aAAO;AAAA,IACL,MAAM;AAAA,MACJ,aAAa,SAAS,kBAAkB;AAAA,QACtC,QAAQ,EAAE,uCAAuC;AAAA,QACjD,MAAM,CAAC,EAAE,SAAS,UAAsB,wBAAI,OAAO,EAAE,MAAM,WAAW,UAAU,SAAS,EAAE,CAAC;AAAA,MAC9F,CAAC;AAAA,MACD,aAAa,SAAS,SAAS;AAAA,QAC7B,QAAQ,EAAE,cAAc;AAAA,QACxB,MAAM,CAAC,EAAE,SAAS,MAAM;AACtB,gBAAM,QAAQ,SAAS;AACvB,gBAAM,QAAQ,yBAAyB,KAAK;AAC5C,gBAAM,kBAAkB,oBAAoB,GAAG,KAAK;AACpD,qBAAuB,wBAAI,YAAY,EAAE,OAAO,cAA0B,wBAAI,QAAQ,EAAE,WAAW,cAAc,UAAU,gBAAgB,CAAC,EAAE,CAAC;AAAA,QACjJ;AAAA,MACF,CAAC;AAAA,MACD,aAAa,SAAS,aAAa;AAAA,QACjC,QAAQ,EAAE,kCAAkC;AAAA,QAC5C,MAAM,CAAC,EAAE,SAAS,MAAM;AAnEhC;AAoEU,gBAAM,SAAQ,cAAS,MAAT,mBAAY;AAC1B,cAAI,CAAC,OAAO;AACV,mBAAO;AAAA,UACT;AACA,gBAAM,kBAAkB,OAAO,OAAO,KAAK,EAAE;AAAA,YAC3C,CAAC,SAAS,KAAK,OAAO;AAAA,UACxB;AACA,gBAAM,iBAAiB,gBAAgB;AAAA,YACrC,CAAC,SAAS,KAAK,OAAO,UAAU;AAAA;AAAA,UAClC;AACA,iBAAO,EAAE,0CAA0C;AAAA,YACjD,WAAW,eAAe;AAAA,YAC1B,OAAO,gBAAgB;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAI,cAAc;AAGlB,IAAI,iCAAiC,CAAC;AAAA,EACpC,WAAW;AAAA,EACX;AACF,MAAM;AACJ,QAAM,MAAM,eAAe,CAAC,KAAK,QAAQ,GAAG,MAAM;AAClD,QAAM,EAAE,QAAQ,GAAG,KAAK,IAAI;AAC5B,QAAM,eAAe;AAAA,IACnB,OAAO;AAAA,IACP,QAAQ,SAAS,SAAS,MAAM,IAAI;AAAA,IACpC,GAAG;AAAA,EACL;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAIA,IAAI,YAAY;AAChB,IAAI,6BAA6B,MAAM;AACrC,QAAM,EAAE,EAAE,IAAI,eAAgB;AAC9B,QAAM,EAAE,cAAc,IAAI,IAAI,+BAA+B;AAAA,IAC3D,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,EAAE,qBAAqB,OAAO,WAAW,SAAS,MAAM,IAAI;AAAA,IAChE;AAAA,MACE,GAAG;AAAA,IACL;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,UAAU,iCAAiC;AACjD,QAAM,EAAE,MAAM,IAAI,aAAa;AAAA,IAC7B,MAAM,uBAAuB,CAAC;AAAA,IAC9B;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,UAAU,CAAC,QAAQ,IAAI;AAAA,EACzB,CAAC;AACD,MAAI,SAAS;AACX,UAAM;AAAA,EACR;AACA,aAAuB,0BAAK,WAAW,EAAE,WAAW,gBAAgB,UAAU;AAAA,QAC5D,oBAAAC,KAAK,OAAO,EAAE,WAAW,+CAA+C,cAA0B,0BAAK,OAAO,EAAE,UAAU;AAAA,UACxH,oBAAAA,KAAK,SAAS,EAAE,UAAU,EAAE,2BAA2B,EAAE,CAAC;AAAA,UAC1D,oBAAAA,KAAK,MAAM,EAAE,WAAW,qBAAqB,MAAM,SAAS,UAAU,EAAE,6BAA6B,EAAE,CAAC;AAAA,IAC1H,EAAE,CAAC,EAAE,CAAC;AAAA,QACU,oBAAAA;AAAA,MACd;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,YAAY,CAAC,QAAQ,GAAG,IAAI,EAAE;AAAA,QAC9B,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,UACT,SAAS,EAAE,0CAA0C;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL;AAIA,IAAI,yBAAyB,MAAM;AACjC,QAAM,EAAE,WAAW,IAAI,aAAa;AACpC,aAAuB,oBAAAC;AAAA,IACrB;AAAA,IACA;AAAA,MACE,SAAS;AAAA,QACP,OAAO,WAAW,qBAAqB;AAAA,QACvC,QAAQ,WAAW,sBAAsB;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,MACX,cAA0B,oBAAAA,KAAK,4BAA4B,CAAC,CAAC;AAAA,IAC/D;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "jsx2", "jsx3"]}