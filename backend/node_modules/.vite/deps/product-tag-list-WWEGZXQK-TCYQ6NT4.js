import {
  useDeleteProductTagAction
} from "./chunk-B6SCVCDW.js";
import {
  useProductTagTableColumns
} from "./chunk-5AGDJHBK.js";
import "./chunk-N55VJRL3.js";
import "./chunk-M4KSNHG3.js";
import "./chunk-FIAZ6EAC.js";
import "./chunk-2BWFXI2Q.js";
import "./chunk-5G2GY7XW.js";
import {
  useProductTagTableQuery
} from "./chunk-UYDDDCMP.js";
import "./chunk-GDXEFZZY.js";
import "./chunk-GEC36FCE.js";
import "./chunk-ITWRYKT3.js";
import "./chunk-AYBSQXJR.js";
import "./chunk-ASBI7JIX.js";
import "./chunk-QG4LHCCG.js";
import "./chunk-I2ZOQM4X.js";
import "./chunk-FDDNE3GJ.js";
import "./chunk-I45JH6GR.js";
import "./chunk-CF4724EP.js";
import "./chunk-QF476XOZ.js";
import "./chunk-MKZD3R7Z.js";
import "./chunk-XYN45SN5.js";
import "./chunk-LVAKEKGS.js";
import "./chunk-5ZQBU3TD.js";
import "./chunk-FWCPP5AI.js";
import "./chunk-XFUFIHVF.js";
import "./chunk-UDMOPZAP.js";
import "./chunk-6Z2VGVOS.js";
import "./chunk-3M3PHA2D.js";
import "./chunk-BL7TK3PG.js";
import "./chunk-VVLYUIEL.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-H3DTEG3J.js";
import "./chunk-32T72GVU.js";
import {
  useProductTagTableFilters
} from "./chunk-3LRISSP5.js";
import "./chunk-P5T2IZP5.js";
import "./chunk-I7242KR3.js";
import "./chunk-OXHGNQBK.js";
import "./chunk-IONS3C54.js";
import "./chunk-XNFM7P3M.js";
import "./chunk-ZIXJCBL3.js";
import "./chunk-WFDZASHE.js";
import "./chunk-Z2AM5IYD.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-U5XHHXWS.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import {
  productTagsQueryKeys,
  useProductTags
} from "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLoaderData
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Container,
  Heading,
  PencilSquare,
  Trash,
  createColumnHelper
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-tag-list-WWEGZXQK.mjs
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var productTagListQuery = (query) => ({
  queryKey: productTagsQueryKeys.list(query),
  queryFn: async () => sdk.admin.productTag.list(query)
});
var productTagListLoader = async ({ request }) => {
  const searchParams = new URL(request.url).searchParams;
  const queryObject = {};
  searchParams.forEach((value, key) => {
    try {
      queryObject[key] = JSON.parse(value);
    } catch (_e) {
      queryObject[key] = value;
    }
  });
  const query = productTagListQuery(
    queryObject
  );
  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);
};
var PAGE_SIZE = 20;
var ProductTagListTable = () => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTagTableQuery({
    pageSize: PAGE_SIZE
  });
  const initialData = useLoaderData();
  const { product_tags, count, isPending, isError, error } = useProductTags(
    searchParams,
    {
      initialData,
      placeholderData: keepPreviousData
    }
  );
  const columns = useColumns();
  const filters = useProductTagTableFilters();
  const { table } = useDataTable({
    data: product_tags,
    count,
    columns,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y px-0 py-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Heading, { children: t("productTags.domain") }),
      (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: "create", children: t("actions.create") }) })
    ] }),
    (0, import_jsx_runtime.jsx)(
      _DataTable,
      {
        table,
        filters,
        queryObject: raw,
        isLoading: isPending,
        columns,
        pageSize: PAGE_SIZE,
        count,
        navigateTo: (row) => row.original.id,
        search: true,
        pagination: true,
        orderBy: [
          { key: "value", label: t("fields.value") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ]
      }
    )
  ] });
};
var ProductTagRowActions = ({
  productTag
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTagAction({ productTag });
  return (0, import_jsx_runtime.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
              label: t("actions.edit"),
              to: `${productTag.id}/edit`
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime.jsx)(Trash, {}),
              label: t("actions.delete"),
              onClick: handleDelete
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useProductTagTableColumns();
  return (0, import_react.useMemo)(
    () => [
      ...base,
      columnHelper.display({
        id: "actions",
        cell: ({ row }) => (0, import_jsx_runtime.jsx)(ProductTagRowActions, { productTag: row.original })
      })
    ],
    [base]
  );
};
var ProductTagList = () => {
  const { getWidgets } = useExtension();
  return (0, import_jsx_runtime2.jsx)(
    SingleColumnPage,
    {
      showMetadata: false,
      showJSON: false,
      hasOutlet: true,
      widgets: {
        after: getWidgets("product_tag.list.after"),
        before: getWidgets("product_tag.list.before")
      },
      children: (0, import_jsx_runtime2.jsx)(ProductTagListTable, {})
    }
  );
};
export {
  ProductTagList as Component,
  productTagListLoader as loader
};
//# sourceMappingURL=product-tag-list-WWEGZXQK-TCYQ6NT4.js.map
