import {
  useDeleteProductTypeAction
} from "./chunk-AAK5OCDV.js";
import {
  useProductTableColumns
} from "./chunk-6Z2VGVOS.js";
import {
  useProductTableQuery
} from "./chunk-3M3PHA2D.js";
import "./chunk-BL7TK3PG.js";
import "./chunk-VVLYUIEL.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-32T72GVU.js";
import {
  useProductTableFilters
} from "./chunk-Z2AM5IYD.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import "./chunk-U5XHHXWS.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import {
  productTypesQueryKeys,
  useProductType
} from "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import {
  useProducts
} from "./chunk-SZTMXX7E.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Container,
  Heading,
  PencilSquare,
  Trash
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-type-detail-B5ROLSWV.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var ProductTypeDetailBreadcrumb = (props) => {
  const { id } = props.params || {};
  const { product_type } = useProductType(id, void 0, {
    initialData: props.data,
    enabled: Boolean(id)
  });
  if (!product_type) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("span", { children: product_type.value });
};
var productTypeDetailQuery = (id) => ({
  queryKey: productTypesQueryKeys.detail(id),
  queryFn: async () => sdk.admin.productType.retrieve(id)
});
var productTypeLoader = async ({ params }) => {
  const id = params.id;
  const query = productTypeDetailQuery(id);
  return queryClient.ensureQueryData(query);
};
var ProductTypeGeneralSection = ({
  productType
}) => {
  const { t } = useTranslation();
  const handleDelete = useDeleteProductTypeAction(
    productType.id,
    productType.value
  );
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "flex items-center justify-between", children: [
    (0, import_jsx_runtime2.jsx)(Heading, { children: productType.value }),
    (0, import_jsx_runtime2.jsx)(
      ActionMenu,
      {
        groups: [
          {
            actions: [
              {
                label: t("actions.edit"),
                icon: (0, import_jsx_runtime2.jsx)(PencilSquare, {}),
                to: "edit"
              }
            ]
          },
          {
            actions: [
              {
                label: t("actions.delete"),
                icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
                onClick: handleDelete
              }
            ]
          }
        ]
      }
    )
  ] });
};
var PAGE_SIZE = 10;
var ProductTypeProductSection = ({
  productType
}) => {
  const { t } = useTranslation();
  const { searchParams, raw } = useProductTableQuery({
    pageSize: PAGE_SIZE
  });
  const { products, count, isPending, isError, error } = useProducts({
    ...searchParams,
    type_id: [productType.id]
  });
  const filters = useProductTableFilters(["product_types"]);
  const columns = useProductTableColumns();
  const { table } = useDataTable({
    columns,
    data: products,
    count: (products == null ? void 0 : products.length) || 0,
    getRowId: (row) => row.id,
    pageSize: PAGE_SIZE
  });
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime3.jsx)("div", { className: "px-6 py-4", children: (0, import_jsx_runtime3.jsx)(Heading, { level: "h2", children: t("products.domain") }) }),
    (0, import_jsx_runtime3.jsx)(
      _DataTable,
      {
        table,
        filters,
        isLoading: isPending,
        columns,
        count,
        pageSize: PAGE_SIZE,
        navigateTo: ({ original }) => `/products/${original.id}`,
        orderBy: [
          { key: "title", label: t("fields.title") },
          { key: "created_at", label: t("fields.createdAt") },
          { key: "updated_at", label: t("fields.updatedAt") }
        ],
        queryObject: raw,
        search: true,
        pagination: true
      }
    )
  ] });
};
var ProductTypeDetail = () => {
  const { id } = useParams();
  const initialData = useLoaderData();
  const { product_type, isPending, isError, error } = useProductType(
    id,
    void 0,
    {
      initialData
    }
  );
  const { getWidgets } = useExtension();
  if (isPending || !product_type) {
    return (0, import_jsx_runtime4.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime4.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        after: getWidgets("product_type.details.after"),
        before: getWidgets("product_type.details.before")
      },
      showJSON: true,
      showMetadata: true,
      data: product_type,
      children: [
        (0, import_jsx_runtime4.jsx)(ProductTypeGeneralSection, { productType: product_type }),
        (0, import_jsx_runtime4.jsx)(ProductTypeProductSection, { productType: product_type })
      ]
    }
  );
};
export {
  ProductTypeDetailBreadcrumb as Breadcrumb,
  ProductTypeDetail as Component,
  productTypeLoader as loader
};
//# sourceMappingURL=product-type-detail-B5ROLSWV-3BONOCX4.js.map
