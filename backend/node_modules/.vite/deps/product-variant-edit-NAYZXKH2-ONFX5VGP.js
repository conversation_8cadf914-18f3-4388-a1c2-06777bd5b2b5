import {
  transformNullableFormData,
  transformNullableFormNumber
} from "./chunk-6JFXN7BR.js";
import {
  CountrySelect
} from "./chunk-ZYZ4UOUY.js";
import {
  Combobox
} from "./chunk-CME5W7KH.js";
import {
  optionalInt
} from "./chunk-7LOZU53L.js";
import "./chunk-EZLR4STK.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import "./chunk-HPGXK5DQ.js";
import "./chunk-IA4ROPJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  z
} from "./chunk-4XXECALA.js";
import "./chunk-MPXR7HT5.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import {
  productVariantQuery<PERSON>eys
} from "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import "./chunk-YXXDSYQ5.js";
import "./chunk-NWAMKOL4.js";
import "./chunk-6TPPQSEA.js";
import "./chunk-5SN5ZDZV.js";
import {
  useProduct,
  useProductVariant,
  useUpdateProductVariant
} from "./chunk-SZTMXX7E.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import {
  sdk
} from "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useLoaderData,
  useParams,
  useSearchParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Divider,
  Heading,
  Input,
  Switch,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/product-variant-edit-NAYZXKH2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var ProductEditVariantSchema = z.object({
  title: z.string().min(1),
  material: z.string().optional(),
  sku: z.string().optional(),
  ean: z.string().optional(),
  upc: z.string().optional(),
  barcode: z.string().optional(),
  manage_inventory: z.boolean(),
  allow_backorder: z.boolean(),
  weight: optionalInt,
  height: optionalInt,
  width: optionalInt,
  length: optionalInt,
  mid_code: z.string().optional(),
  hs_code: z.string().optional(),
  origin_country: z.string().optional(),
  options: z.record(z.string())
});
var ProductEditVariantForm = ({
  variant,
  product
}) => {
  var _a, _b;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const defaultOptions = (_a = product.options) == null ? void 0 : _a.reduce((acc, option) => {
    var _a2;
    const varOpt = (_a2 = variant.options) == null ? void 0 : _a2.find((o) => o.option_id === option.id);
    acc[option.title] = varOpt == null ? void 0 : varOpt.value;
    return acc;
  }, {});
  const form = useForm({
    defaultValues: {
      title: variant.title || "",
      material: variant.material || "",
      sku: variant.sku || "",
      ean: variant.ean || "",
      upc: variant.upc || "",
      barcode: variant.barcode || "",
      manage_inventory: variant.manage_inventory || false,
      allow_backorder: variant.allow_backorder || false,
      weight: variant.weight || "",
      height: variant.height || "",
      width: variant.width || "",
      length: variant.length || "",
      mid_code: variant.mid_code || "",
      hs_code: variant.hs_code || "",
      origin_country: variant.origin_country || "",
      options: defaultOptions
    },
    resolver: t(ProductEditVariantSchema)
  });
  const { mutateAsync, isPending } = useUpdateProductVariant(
    variant.product_id,
    variant.id
  );
  const handleSubmit = form.handleSubmit(async (data) => {
    const {
      title,
      weight,
      height,
      width,
      length,
      allow_backorder,
      manage_inventory,
      options,
      ...optional
    } = data;
    const nullableData = transformNullableFormData(optional);
    await mutateAsync(
      {
        id: variant.id,
        weight: transformNullableFormNumber(weight),
        height: transformNullableFormNumber(height),
        width: transformNullableFormNumber(width),
        length: transformNullableFormNumber(length),
        title,
        allow_backorder,
        manage_inventory,
        options,
        ...nullableData
      },
      {
        onSuccess: () => {
          handleSuccess("../");
          toast.success(t2("products.variant.edit.success"));
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex size-full flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex size-full flex-col gap-y-8 overflow-auto", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "title",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "material",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.material") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (_b = product.options) == null ? void 0 : _b.map((option) => {
              return (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: `options.${option.title}`,
                  render: ({ field: { value, onChange, ...field } }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { children: option.title }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                        Combobox,
                        {
                          value,
                          onChange: (v) => {
                            onChange(v);
                          },
                          ...field,
                          options: option.values.map((v) => ({
                            label: v.value,
                            value: v.value
                          }))
                        }
                      ) })
                    ] });
                  }
                },
                option.id
              );
            })
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
            (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
              (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t2("products.variant.inventory.header") }),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "sku",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.sku") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "ean",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.ean") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "upc",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.upc") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              ),
              (0, import_jsx_runtime.jsx)(
                Form.Field,
                {
                  control: form.control,
                  name: "barcode",
                  render: ({ field }) => {
                    return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                      (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.barcode") }),
                      (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                      (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                    ] });
                  }
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "manage_inventory",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.manageInventoryLabel") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            checked: value,
                            onCheckedChange: (checked) => onChange(!!checked),
                            ...field
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.manageInventoryHint") })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "allow_backorder",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-1", children: [
                      (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between", children: [
                        (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("products.variant.inventory.allowBackordersLabel") }),
                        (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                          Switch,
                          {
                            checked: value,
                            onCheckedChange: (checked) => onChange(!!checked),
                            ...field
                          }
                        ) })
                      ] }),
                      (0, import_jsx_runtime.jsx)(Form.Hint, { children: t2("products.variant.inventory.allowBackordersHint") })
                    ] }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(Divider, {}),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(Heading, { level: "h2", children: t2("products.attributes") }),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "weight",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.weight") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "width",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.width") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "length",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.length") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "height",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.height") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { type: "number", ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "mid_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.midCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "hs_code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.hsCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "origin_country",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { optional: true, children: t2("fields.countryOfOrigin") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(CountrySelect, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] })
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "small", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "small", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var ProductVariantEdit = () => {
  const initialData = useLoaderData();
  const { t: t2 } = useTranslation();
  const { id, variant_id } = useParams();
  const [URLSearchParms] = useSearchParams();
  const searchVariantId = URLSearchParms.get("variant_id");
  const { variant, isPending, isError, error } = useProductVariant(
    id,
    variant_id || searchVariantId,
    void 0,
    {
      initialData
    }
  );
  const {
    product,
    isPending: isProductPending,
    isError: isProductError,
    error: productError
  } = useProduct(
    variant == null ? void 0 : variant.product_id,
    {
      fields: "-variants"
    },
    {
      enabled: !!(variant == null ? void 0 : variant.product_id)
    }
  );
  const ready = !isPending && !!variant && !isProductPending && !!product;
  if (isError) {
    throw error;
  }
  if (isProductError) {
    throw productError;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("products.variant.edit.header") }) }),
    ready && (0, import_jsx_runtime2.jsx)(ProductEditVariantForm, { variant, product })
  ] });
};
var queryFn = async (id, variantId) => {
  return await sdk.admin.product.retrieveVariant(id, variantId);
};
var editProductVariantQuery = (id, variantId) => ({
  queryKey: productVariantQueryKeys.detail(variantId),
  queryFn: async () => queryFn(id, variantId)
});
var editProductVariantLoader = async ({
  params,
  request
}) => {
  const id = params.id;
  const searchParams = new URL(request.url).searchParams;
  const searchVariantId = searchParams.get("variant_id");
  const variantId = params.variant_id || searchVariantId;
  const query = editProductVariantQuery(id, variantId || searchVariantId);
  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);
};
export {
  ProductVariantEdit as Component,
  editProductVariantLoader as loader
};
//# sourceMappingURL=product-variant-edit-NAYZXKH2-ONFX5VGP.js.map
