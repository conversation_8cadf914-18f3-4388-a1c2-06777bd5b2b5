import {
  PercentageInput
} from "./chunk-W4P4VL57.js";
import {
  SwitchBox
} from "./chunk-OTCB5CPQ.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  z
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm
} from "./chunk-IL7M46GI.js";
import {
  useTaxRate,
  useUpdateTaxRate
} from "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  Button,
  Heading,
  Input,
  toast
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/tax-region-tax-rate-edit-VACYUP6R.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var TaxRegionTaxRateEditSchema = z.object({
  name: z.string().min(1),
  code: z.string().optional(),
  rate: z.object({
    float: z.number().optional(),
    value: z.string().optional()
  }),
  is_combinable: z.boolean().optional()
});
var TaxRegionTaxRateEditForm = ({
  taxRate,
  isSublevel = false
}) => {
  var _a;
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      name: taxRate.name,
      code: taxRate.code,
      rate: {
        value: ((_a = taxRate.rate) == null ? void 0 : _a.toString()) || ""
      },
      is_combinable: taxRate.is_combinable
    },
    resolver: t(TaxRegionTaxRateEditSchema)
  });
  const { mutateAsync, isPending } = useUpdateTaxRate(taxRate.id);
  const handleSubmit = form.handleSubmit(async (values) => {
    var _a2;
    await mutateAsync(
      {
        name: values.name,
        code: values.code,
        rate: (_a2 = values.rate) == null ? void 0 : _a2.float,
        is_combinable: values.is_combinable
      },
      {
        onSuccess: () => {
          toast.success(t2("taxRegions.taxRates.edit.successToast"));
          handleSuccess();
        },
        onError: (error) => {
          toast.error(error.message);
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      className: "flex flex-1 flex-col overflow-hidden",
      onSubmit: handleSubmit,
      children: [
        (0, import_jsx_runtime.jsxs)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-6 overflow-auto", children: [
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "name",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("fields.name") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxCode") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "rate",
                render: ({ field: { value, onChange, ...field } }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("taxRegions.fields.taxRate") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(
                      PercentageInput,
                      {
                        ...field,
                        value: value == null ? void 0 : value.value,
                        onValueChange: (value2, _name, values) => onChange({
                          value: value2,
                          float: values == null ? void 0 : values.float
                        })
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                  ] });
                }
              }
            )
          ] }),
          isSublevel && (0, import_jsx_runtime.jsx)(
            SwitchBox,
            {
              control: form.control,
              name: "is_combinable",
              label: t2("taxRegions.fields.isCombinable.label"),
              description: t2("taxRegions.fields.isCombinable.hint")
            }
          )
        ] }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { className: "shrink-0", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var TaxRegionEdit = () => {
  const { t: t2 } = useTranslation();
  const { province_id, tax_rate_id } = useParams();
  const { tax_rate, isPending, isError, error } = useTaxRate(tax_rate_id);
  const ready = !isPending && !!tax_rate;
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsxs)(RouteDrawer.Header, { children: [
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Title, { asChild: true, children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("taxRegions.taxRates.edit.header") }) }),
      (0, import_jsx_runtime2.jsx)(RouteDrawer.Description, { className: "sr-only", children: t2("taxRegions.taxRates.edit.hint") })
    ] }),
    ready && (0, import_jsx_runtime2.jsx)(
      TaxRegionTaxRateEditForm,
      {
        taxRate: tax_rate,
        isSublevel: !!province_id
      }
    )
  ] });
};
export {
  TaxRegionEdit as Component
};
//# sourceMappingURL=tax-region-tax-rate-edit-VACYUP6R-RO6JB5DC.js.map
