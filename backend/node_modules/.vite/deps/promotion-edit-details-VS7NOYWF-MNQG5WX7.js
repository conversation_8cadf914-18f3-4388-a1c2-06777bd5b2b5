import {
  DeprecatedPercentageInput
} from "./chunk-W4P4VL57.js";
import {
  getCurrencySymbol
} from "./chunk-H3DTEG3J.js";
import "./chunk-EGRHWZRV.js";
import {
  KeyboundForm
} from "./chunk-DP54EP6X.js";
import {
  RouteDrawer,
  useRouteModal
} from "./chunk-L2KJ2QJA.js";
import {
  t
} from "./chunk-7FOP5RO4.js";
import {
  enumType,
  numberType,
  objectType,
  stringType
} from "./chunk-4XXECALA.js";
import {
  Form,
  useForm,
  useWatch
} from "./chunk-IL7M46GI.js";
import {
  usePromotion,
  useUpdatePromotion
} from "./chunk-CN6R4DBW.js";
import "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  Trans,
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  useParams
} from "./chunk-T7YBVUWZ.js";
import {
  But<PERSON>,
  CurrencyInput2 as CurrencyInput,
  Heading,
  Input,
  RadioGroup,
  Text
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/promotion-edit-details-VS7NOYWF.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var EditPromotionSchema = objectType({
  is_automatic: stringType().toLowerCase(),
  code: stringType().min(1),
  status: enumType(["active", "inactive", "draft"]),
  value_type: enumType(["fixed", "percentage"]),
  value: numberType(),
  allocation: enumType(["each", "across"])
});
var EditPromotionDetailsForm = ({
  promotion
}) => {
  const { t: t2 } = useTranslation();
  const { handleSuccess } = useRouteModal();
  const form = useForm({
    defaultValues: {
      is_automatic: promotion.is_automatic.toString(),
      code: promotion.code,
      status: promotion.status,
      value: promotion.application_method.value,
      allocation: promotion.application_method.allocation,
      value_type: promotion.application_method.type
    },
    resolver: t(EditPromotionSchema)
  });
  const watchValueType = useWatch({
    control: form.control,
    name: "value_type"
  });
  const isFixedValueType = watchValueType === "fixed";
  const { mutateAsync, isPending } = useUpdatePromotion(promotion.id);
  const handleSubmit = form.handleSubmit(async (data) => {
    await mutateAsync(
      {
        is_automatic: data.is_automatic === "true",
        code: data.code,
        status: data.status,
        application_method: {
          value: data.value,
          type: data.value_type,
          allocation: data.allocation
        }
      },
      {
        onSuccess: () => {
          handleSuccess();
        }
      }
    );
  });
  return (0, import_jsx_runtime.jsx)(RouteDrawer.Form, { form, children: (0, import_jsx_runtime.jsxs)(
    KeyboundForm,
    {
      onSubmit: handleSubmit,
      className: "flex flex-1 flex-col overflow-hidden",
      children: [
        (0, import_jsx_runtime.jsx)(RouteDrawer.Body, { className: "flex flex-1 flex-col gap-y-8 overflow-y-auto", children: (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-8", children: [
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "status",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.status.label") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                    RadioGroup,
                    {
                      className: "flex-col gap-y-3",
                      ...field,
                      value: field.value,
                      onValueChange: field.onChange,
                      children: [
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "draft",
                            label: t2("promotions.form.status.draft.title"),
                            description: t2(
                              "promotions.form.status.draft.description"
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "active",
                            label: t2("promotions.form.status.active.title"),
                            description: t2(
                              "promotions.form.status.active.description"
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "inactive",
                            label: t2("promotions.form.status.inactive.title"),
                            description: t2(
                              "promotions.form.status.inactive.description"
                            )
                          }
                        )
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "is_automatic",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.method.label") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                    RadioGroup,
                    {
                      className: "flex-col gap-y-3",
                      ...field,
                      value: field.value,
                      onValueChange: field.onChange,
                      children: [
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "false",
                            label: t2("promotions.form.method.code.title"),
                            description: t2(
                              "promotions.form.method.code.description"
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "true",
                            label: t2("promotions.form.method.automatic.title"),
                            description: t2(
                              "promotions.form.method.automatic.description"
                            )
                          }
                        )
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsxs)("div", { className: "flex flex-col gap-y-4", children: [
            (0, import_jsx_runtime.jsx)(
              Form.Field,
              {
                control: form.control,
                name: "code",
                render: ({ field }) => {
                  return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                    (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.form.code.title") }),
                    (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsx)(Input, { ...field }) })
                  ] });
                }
              }
            ),
            (0, import_jsx_runtime.jsx)(
              Text,
              {
                size: "small",
                leading: "compact",
                className: "text-ui-fg-subtle",
                children: (0, import_jsx_runtime.jsx)(
                  Trans,
                  {
                    t: t2,
                    i18nKey: "promotions.form.code.description",
                    components: [(0, import_jsx_runtime.jsx)("br", {}, "break")]
                  }
                )
              }
            )
          ] }),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "value_type",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.value_type") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                    RadioGroup,
                    {
                      className: "flex-col gap-y-3",
                      ...field,
                      onValueChange: field.onChange,
                      children: [
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "fixed",
                            label: t2("promotions.form.value_type.fixed.title"),
                            description: t2(
                              "promotions.form.value_type.fixed.description"
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "percentage",
                            label: t2(
                              "promotions.form.value_type.percentage.title"
                            ),
                            description: t2(
                              "promotions.form.value_type.percentage.description"
                            )
                          }
                        )
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "value",
              render: ({ field: { onChange, ...field } }) => {
                var _a;
                const currencyCode = ((_a = promotion.application_method) == null ? void 0 : _a.currency_code) ?? "USD";
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: isFixedValueType ? t2("fields.amount") : t2("fields.percentage") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: isFixedValueType ? (0, import_jsx_runtime.jsx)(
                    CurrencyInput,
                    {
                      min: 0,
                      onValueChange: (val) => onChange(val ? parseInt(val) : null),
                      code: currencyCode,
                      symbol: getCurrencySymbol(currencyCode),
                      ...field,
                      value: field.value
                    }
                  ) : (0, import_jsx_runtime.jsx)(
                    DeprecatedPercentageInput,
                    {
                      min: 0,
                      max: 100,
                      ...field,
                      value: field.value || "",
                      onChange: (e) => {
                        onChange(
                          e.target.value === "" ? null : parseInt(e.target.value)
                        );
                      }
                    },
                    "amount"
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Form.Field,
            {
              control: form.control,
              name: "allocation",
              render: ({ field }) => {
                return (0, import_jsx_runtime.jsxs)(Form.Item, { children: [
                  (0, import_jsx_runtime.jsx)(Form.Label, { children: t2("promotions.fields.allocation") }),
                  (0, import_jsx_runtime.jsx)(Form.Control, { children: (0, import_jsx_runtime.jsxs)(
                    RadioGroup,
                    {
                      className: "flex-col gap-y-3",
                      ...field,
                      onValueChange: field.onChange,
                      children: [
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "each",
                            label: t2("promotions.form.allocation.each.title"),
                            description: t2(
                              "promotions.form.allocation.each.description"
                            )
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(
                          RadioGroup.ChoiceBox,
                          {
                            value: "across",
                            label: t2("promotions.form.allocation.across.title"),
                            description: t2(
                              "promotions.form.allocation.across.description"
                            )
                          }
                        )
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Form.ErrorMessage, {})
                ] });
              }
            }
          )
        ] }) }),
        (0, import_jsx_runtime.jsx)(RouteDrawer.Footer, { children: (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-end gap-x-2", children: [
          (0, import_jsx_runtime.jsx)(RouteDrawer.Close, { asChild: true, children: (0, import_jsx_runtime.jsx)(Button, { size: "small", variant: "secondary", children: t2("actions.cancel") }) }),
          (0, import_jsx_runtime.jsx)(Button, { size: "small", type: "submit", isLoading: isPending, children: t2("actions.save") })
        ] }) })
      ]
    }
  ) });
};
var PromotionEditDetails = () => {
  const { id } = useParams();
  const { t: t2 } = useTranslation();
  const { promotion, isLoading, isError, error } = usePromotion(id);
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime2.jsxs)(RouteDrawer, { children: [
    (0, import_jsx_runtime2.jsx)(RouteDrawer.Header, { children: (0, import_jsx_runtime2.jsx)(Heading, { children: t2("promotions.edit.title") }) }),
    !isLoading && promotion && (0, import_jsx_runtime2.jsx)(EditPromotionDetailsForm, { promotion })
  ] });
};
export {
  PromotionEditDetails as Component
};
//# sourceMappingURL=promotion-edit-details-VS7NOYWF-MNQG5WX7.js.map
