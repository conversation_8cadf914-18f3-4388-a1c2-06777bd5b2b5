import {
  useCurrenciesTableColumns,
  useCurrenciesTableQuery
} from "./chunk-52CDFLXQ.js";
import "./chunk-XFUFIHVF.js";
import {
  StatusCell
} from "./chunk-VVLYUIEL.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S5JEKJNE.js";
import {
  _DataTable,
  useDataTable
} from "./chunk-L5JDKY2Z.js";
import "./chunk-EG2I6FVI.js";
import "./chunk-5SXR5VZM.js";
import "./chunk-YD4BSUSY.js";
import {
  SingleColumnPage
} from "./chunk-4BZDPWQC.js";
import "./chunk-32T72GVU.js";
import "./chunk-7CVWI3VK.js";
import "./chunk-QX6SXRUW.js";
import "./chunk-7ANVLPZR.js";
import "./chunk-WP4SM64O.js";
import {
  useExtension
} from "./chunk-FTD3ZWHZ.js";
import {
  SingleColumnPageSkeleton
} from "./chunk-XXSIULXV.js";
import {
  ActionMenu
} from "./chunk-6P6DQHDD.js";
import "./chunk-LE3JFLDU.js";
import "./chunk-GWZBAACX.js";
import "./chunk-NV2N3EWM.js";
import "./chunk-QKV675OM.js";
import "./chunk-7UAYECTW.js";
import "./chunk-Y3NYV3NU.js";
import "./chunk-MPXR7HT5.js";
import "./chunk-YBKYAB3X.js";
import "./chunk-WNILWPA2.js";
import "./chunk-QH7WL7BE.js";
import "./chunk-UHLJOZH7.js";
import "./chunk-EQVBGHHK.js";
import "./chunk-E4TWOBGY.js";
import "./chunk-B7WS6CWS.js";
import "./chunk-ASM3JVNX.js";
import "./chunk-R73OU4H7.js";
import {
  useCurrencies
} from "./chunk-43FR2ATH.js";
import "./chunk-LEOMM6TE.js";
import "./chunk-QGTAAHL2.js";
import "./chunk-Y6WFHOFY.js";
import "./chunk-EDOX6CCV.js";
import "./chunk-43QMFFE5.js";
import "./chunk-7JWGOBEJ.js";
import "./chunk-CN6R4DBW.js";
import "./chunk-T4GTGXJ6.js";
import {
  useStockLocation
} from "./chunk-FLXIB6AG.js";
import "./chunk-66SOOYSD.js";
import "./chunk-QBO47LXF.js";
import "./chunk-MDHM6O7Z.js";
import {
  retrieveActiveStore,
  storeQueryKeys,
  useStore,
  useUpdateStore
} from "./chunk-YXXDSYQ5.js";
import {
  useRegion
} from "./chunk-NWAMKOL4.js";
import {
  usePricePreferences
} from "./chunk-6TPPQSEA.js";
import {
  useSalesChannel
} from "./chunk-5SN5ZDZV.js";
import "./chunk-SZTMXX7E.js";
import {
  queryClient
} from "./chunk-VMLNCWLE.js";
import "./chunk-CUPZIPFX.js";
import "./chunk-DVDTANCJ.js";
import {
  useTranslation
} from "./chunk-7HWTQOXJ.js";
import "./chunk-5GF3RGIE.js";
import {
  Link,
  useLoaderData
} from "./chunk-T7YBVUWZ.js";
import {
  Badge,
  CheckCircle,
  Checkbox,
  CommandBar,
  Container,
  Heading,
  PencilSquare,
  Plus,
  Text,
  Trash,
  XCircle,
  createColumnHelper,
  toast,
  usePrompt
} from "./chunk-LMS3YZZY.js";
import "./chunk-5GLF3XJW.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-3GMSJT6Y.js";
import {
  keepPreviousData
} from "./chunk-R35JBZ3G.js";
import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@medusajs/dashboard/dist/store-detail-T5YBEUMC.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var storeDetailQuery = () => ({
  queryKey: storeQueryKeys.details(),
  queryFn: async () => retrieveActiveStore()
});
var storeLoader = async () => {
  const query = storeDetailQuery();
  return queryClient.getQueryData(query.queryKey) ?? await queryClient.fetchQuery(query);
};
var StoreGeneralSection = ({ store }) => {
  var _a, _b, _c;
  const { t } = useTranslation();
  const { region } = useRegion(store.default_region_id, void 0, {
    enabled: !!store.default_region_id
  });
  const defaultCurrency = (_a = store.supported_currencies) == null ? void 0 : _a.find((c) => c.is_default);
  const { sales_channel } = useSalesChannel(store.default_sales_channel_id, {
    enabled: !!store.default_sales_channel_id
  });
  const { stock_location } = useStockLocation(
    store.default_location_id,
    {
      fields: "id,name"
    },
    {
      enabled: !!store.default_location_id
    }
  );
  return (0, import_jsx_runtime.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime.jsxs)("div", { children: [
        (0, import_jsx_runtime.jsx)(Heading, { children: t("store.domain") }),
        (0, import_jsx_runtime.jsx)(Text, { className: "text-ui-fg-subtle", size: "small", children: t("store.manageYourStoresDetails") })
      ] }),
      (0, import_jsx_runtime.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime.jsx)(PencilSquare, {}),
                  label: t("actions.edit"),
                  to: "edit"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("fields.name") }),
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: store.name })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("store.defaultCurrency") }),
      defaultCurrency ? (0, import_jsx_runtime.jsxs)("div", { className: "flex items-center gap-x-2", children: [
        (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", children: (_b = defaultCurrency.currency_code) == null ? void 0 : _b.toUpperCase() }),
        (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: (_c = defaultCurrency.currency) == null ? void 0 : _c.name })
      ] }) : (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "-" })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("store.defaultRegion") }),
      (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-x-2", children: region ? (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: `/settings/regions/${region.id}`, children: region.name }) }) : (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "-" }) })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("store.defaultSalesChannel") }),
      (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-x-2", children: sales_channel ? (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: `/settings/sales-channels/${sales_channel.id}`, children: sales_channel.name }) }) : (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "-" }) })
    ] }),
    (0, import_jsx_runtime.jsxs)("div", { className: "text-ui-fg-subtle grid grid-cols-2 px-6 py-4", children: [
      (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", weight: "plus", children: t("store.defaultLocation") }),
      (0, import_jsx_runtime.jsx)("div", { className: "flex items-center gap-x-2", children: stock_location ? (0, import_jsx_runtime.jsx)(Badge, { size: "2xsmall", asChild: true, children: (0, import_jsx_runtime.jsx)(Link, { to: `/settings/locations/${stock_location.id}`, children: stock_location.name }) }) : (0, import_jsx_runtime.jsx)(Text, { size: "small", leading: "compact", children: "-" }) })
    ] })
  ] });
};
var PAGE_SIZE = 10;
var StoreCurrencySection = ({ store }) => {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  const [rowSelection, setRowSelection] = (0, import_react.useState)({});
  const { searchParams, raw } = useCurrenciesTableQuery({ pageSize: PAGE_SIZE });
  const {
    currencies,
    count,
    isPending: isCurrenciesPending,
    isError: isCurrenciesError,
    error: currenciesError
  } = useCurrencies(
    {
      code: (_a = store.supported_currencies) == null ? void 0 : _a.map((c) => c.currency_code),
      ...searchParams
    },
    {
      placeholderData: keepPreviousData,
      enabled: !!((_b = store.supported_currencies) == null ? void 0 : _b.length)
    }
  );
  const {
    price_preferences: pricePreferences,
    isPending: isPricePreferencesPending,
    isError: isPricePreferencesError,
    error: pricePreferencesError
  } = usePricePreferences(
    {
      attribute: "currency_code",
      value: (_c = store.supported_currencies) == null ? void 0 : _c.map((c) => c.currency_code)
    },
    {
      enabled: !!((_d = store.supported_currencies) == null ? void 0 : _d.length)
    }
  );
  const columns = useColumns();
  const prefMap = (0, import_react.useMemo)(() => {
    return new Map(pricePreferences == null ? void 0 : pricePreferences.map((pref) => [pref.value, pref]));
  }, [pricePreferences]);
  const withTaxInclusivity = currencies == null ? void 0 : currencies.map((c) => {
    var _a2;
    return {
      ...c,
      is_tax_inclusive: (_a2 = prefMap.get(c.code)) == null ? void 0 : _a2.is_tax_inclusive
    };
  });
  const { table } = useDataTable({
    data: withTaxInclusivity ?? [],
    columns,
    count,
    getRowId: (row) => row.code,
    rowSelection: {
      state: rowSelection,
      updater: setRowSelection
    },
    enablePagination: true,
    enableRowSelection: true,
    pageSize: PAGE_SIZE,
    meta: {
      storeId: store.id,
      supportedCurrencies: store.supported_currencies,
      defaultCurrencyCode: (_f = (_e = store.supported_currencies) == null ? void 0 : _e.find((c) => c.is_default)) == null ? void 0 : _f.currency_code,
      preferencesMap: prefMap
    }
  });
  const { mutateAsync } = useUpdateStore(store.id);
  const { t } = useTranslation();
  const prompt = usePrompt();
  const handleDeleteCurrencies = async () => {
    var _a2;
    const ids = Object.keys(rowSelection);
    const result = await prompt({
      title: t("general.areYouSure"),
      description: t("store.removeCurrencyWarning", {
        count: ids.length
      }),
      confirmText: t("actions.remove"),
      cancelText: t("actions.cancel")
    });
    if (!result) {
      return;
    }
    await mutateAsync(
      {
        supported_currencies: ((_a2 = store.supported_currencies) == null ? void 0 : _a2.filter(
          (c) => !ids.includes(c.currency_code)
        )) ?? []
      },
      {
        onSuccess: () => {
          setRowSelection({});
          toast.success(t("store.toast.currenciesRemoved"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  if (isCurrenciesError) {
    throw currenciesError;
  }
  if (isPricePreferencesError) {
    throw pricePreferencesError;
  }
  const isLoading = isCurrenciesPending || isPricePreferencesPending;
  return (0, import_jsx_runtime2.jsxs)(Container, { className: "divide-y p-0", children: [
    (0, import_jsx_runtime2.jsxs)("div", { className: "flex items-center justify-between px-6 py-4", children: [
      (0, import_jsx_runtime2.jsx)(Heading, { level: "h2", children: t("store.currencies") }),
      (0, import_jsx_runtime2.jsx)(
        ActionMenu,
        {
          groups: [
            {
              actions: [
                {
                  icon: (0, import_jsx_runtime2.jsx)(Plus, {}),
                  label: t("actions.add"),
                  to: "currencies"
                }
              ]
            }
          ]
        }
      )
    ] }),
    (0, import_jsx_runtime2.jsx)(
      _DataTable,
      {
        orderBy: [
          { key: "name", label: t("fields.name") },
          { key: "code", label: t("fields.code") }
        ],
        search: true,
        pagination: true,
        table,
        pageSize: PAGE_SIZE,
        columns,
        count: !((_g = store.supported_currencies) == null ? void 0 : _g.length) ? 0 : count,
        isLoading: !((_h = store.supported_currencies) == null ? void 0 : _h.length) ? false : isLoading,
        queryObject: raw
      }
    ),
    (0, import_jsx_runtime2.jsx)(CommandBar, { open: !!Object.keys(rowSelection).length, children: (0, import_jsx_runtime2.jsxs)(CommandBar.Bar, { children: [
      (0, import_jsx_runtime2.jsx)(CommandBar.Value, { children: t("general.countSelected", {
        count: Object.keys(rowSelection).length
      }) }),
      (0, import_jsx_runtime2.jsx)(CommandBar.Seperator, {}),
      (0, import_jsx_runtime2.jsx)(
        CommandBar.Command,
        {
          action: handleDeleteCurrencies,
          shortcut: "r",
          label: t("actions.remove")
        }
      )
    ] }) })
  ] });
};
var CurrencyActions = ({
  storeId,
  currency,
  supportedCurrencies,
  defaultCurrencyCode,
  preferencesMap
}) => {
  var _a, _b;
  const { mutateAsync } = useUpdateStore(storeId);
  const { t } = useTranslation();
  const prompt = usePrompt();
  const handleRemove = async () => {
    const result = await prompt({
      title: t("general.areYouSure"),
      description: t("store.removeCurrencyWarning", {
        count: 1
      }),
      verificationInstruction: t("general.typeToConfirm"),
      verificationText: currency.name,
      confirmText: t("actions.remove"),
      cancelText: t("actions.cancel")
    });
    if (!result) {
      return;
    }
    await mutateAsync(
      {
        supported_currencies: supportedCurrencies.filter(
          (c) => c.currency_code !== currency.code
        )
      },
      {
        onSuccess: () => {
          toast.success(t("store.toast.currenciesRemoved"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  const handleToggleTaxInclusivity = async () => {
    await mutateAsync(
      {
        supported_currencies: supportedCurrencies.map((c) => {
          const pref = preferencesMap.get(c.currency_code);
          return {
            ...c,
            is_tax_inclusive: c.currency_code === currency.code ? !(pref == null ? void 0 : pref.is_tax_inclusive) : void 0
          };
        })
      },
      {
        onSuccess: () => {
          toast.success(t("store.toast.updatedTaxInclusivitySuccessfully"));
        },
        onError: (e) => {
          toast.error(e.message);
        }
      }
    );
  };
  return (0, import_jsx_runtime2.jsx)(
    ActionMenu,
    {
      groups: [
        {
          actions: [
            {
              icon: ((_a = preferencesMap.get(currency.code)) == null ? void 0 : _a.is_tax_inclusive) ? (0, import_jsx_runtime2.jsx)(XCircle, {}) : (0, import_jsx_runtime2.jsx)(CheckCircle, {}),
              label: ((_b = preferencesMap.get(currency.code)) == null ? void 0 : _b.is_tax_inclusive) ? t("store.disableTaxInclusivePricing") : t("store.enableTaxInclusivePricing"),
              onClick: handleToggleTaxInclusivity
            }
          ]
        },
        {
          actions: [
            {
              icon: (0, import_jsx_runtime2.jsx)(Trash, {}),
              label: t("actions.remove"),
              onClick: handleRemove,
              disabled: currency.code === defaultCurrencyCode
            }
          ]
        }
      ]
    }
  );
};
var columnHelper = createColumnHelper();
var useColumns = () => {
  const base = useCurrenciesTableColumns();
  const { t } = useTranslation();
  return (0, import_react.useMemo)(
    () => [
      columnHelper.display({
        id: "select",
        header: ({ table }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: table.getIsSomePageRowsSelected() ? "indeterminate" : table.getIsAllPageRowsSelected(),
              onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value)
            }
          );
        },
        cell: ({ row }) => {
          return (0, import_jsx_runtime2.jsx)(
            Checkbox,
            {
              checked: row.getIsSelected(),
              onCheckedChange: (value) => row.toggleSelected(!!value),
              onClick: (e) => {
                e.stopPropagation();
              }
            }
          );
        }
      }),
      ...base,
      columnHelper.accessor("is_tax_inclusive", {
        header: t("fields.taxInclusivePricing"),
        cell: ({ getValue }) => {
          const isTaxInclusive = getValue();
          return (0, import_jsx_runtime2.jsx)(StatusCell, { color: isTaxInclusive ? "green" : "grey", children: isTaxInclusive ? t("fields.true") : t("fields.false") });
        }
      }),
      columnHelper.display({
        id: "actions",
        cell: ({ row, table }) => {
          const {
            supportedCurrencies,
            storeId,
            defaultCurrencyCode,
            preferencesMap
          } = table.options.meta;
          return (0, import_jsx_runtime2.jsx)(
            CurrencyActions,
            {
              storeId,
              currency: row.original,
              supportedCurrencies,
              defaultCurrencyCode,
              preferencesMap
            }
          );
        }
      })
    ],
    [base, t]
  );
};
var StoreDetail = () => {
  const initialData = useLoaderData();
  const { store, isPending, isError, error } = useStore(void 0, {
    initialData
  });
  const { getWidgets } = useExtension();
  if (isPending || !store) {
    return (0, import_jsx_runtime3.jsx)(SingleColumnPageSkeleton, { sections: 2, showJSON: true, showMetadata: true });
  }
  if (isError) {
    throw error;
  }
  return (0, import_jsx_runtime3.jsxs)(
    SingleColumnPage,
    {
      widgets: {
        before: getWidgets("store.details.before"),
        after: getWidgets("store.details.after")
      },
      data: store,
      hasOutlet: true,
      showMetadata: true,
      showJSON: true,
      children: [
        (0, import_jsx_runtime3.jsx)(StoreGeneralSection, { store }),
        (0, import_jsx_runtime3.jsx)(StoreCurrencySection, { store })
      ]
    }
  );
};
export {
  StoreDetail as Component,
  storeLoader as loader
};
//# sourceMappingURL=store-detail-T5YBEUMC-52PXL6DG.js.map
