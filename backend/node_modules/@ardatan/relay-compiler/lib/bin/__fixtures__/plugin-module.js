/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 * @format
 * @emails oncall+relay
 */
// flowlint ambiguous-object-type:error
'use strict';

var RelayLanguagePluginJavaScript = require('../../language/javascript/RelayLanguagePluginJavaScript');

module.exports = RelayLanguagePluginJavaScript;