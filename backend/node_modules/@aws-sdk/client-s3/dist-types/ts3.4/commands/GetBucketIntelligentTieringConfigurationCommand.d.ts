import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketIntelligentTieringConfigurationOutput,
  GetBucketIntelligentTieringConfigurationRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketIntelligentTieringConfigurationCommandInput
  extends GetBucketIntelligentTieringConfigurationRequest {}
export interface GetBucketIntelligentTieringConfigurationCommandOutput
  extends GetBucketIntelligentTieringConfigurationOutput,
    __MetadataBearer {}
declare const GetBucketIntelligentTieringConfigurationCommand_base: {
  new (
    input: GetBucketIntelligentTieringConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketIntelligentTieringConfigurationCommandInput,
    GetBucketIntelligentTieringConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: GetBucketIntelligentTieringConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketIntelligentTieringConfigurationCommandInput,
    GetBucketIntelligentTieringConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketIntelligentTieringConfigurationCommand extends GetBucketIntelligentTieringConfigurationCommand_base {
  protected static __types: {
    api: {
      input: GetBucketIntelligentTieringConfigurationRequest;
      output: GetBucketIntelligentTieringConfigurationOutput;
    };
    sdk: {
      input: GetBucketIntelligentTieringConfigurationCommandInput;
      output: GetBucketIntelligentTieringConfigurationCommandOutput;
    };
  };
}
