import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketWebsiteOutput,
  GetBucketWebsiteRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketWebsiteCommandInput extends GetBucketWebsiteRequest {}
export interface GetBucketWebsiteCommandOutput
  extends GetBucketWebsiteOutput,
    __MetadataBearer {}
declare const GetBucketWebsiteCommand_base: {
  new (
    input: GetBucketWebsiteCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketWebsiteCommandInput,
    GetBucketWebsiteCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: GetBucketWebsiteCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketWebsiteCommandInput,
    GetBucketWebsiteCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketWebsiteCommand extends GetBucketWebsiteCommand_base {
  protected static __types: {
    api: {
      input: GetBucketWebsiteRequest;
      output: GetBucketWebsiteOutput;
    };
    sdk: {
      input: GetBucketWebsiteCommandInput;
      output: GetBucketWebsiteCommandOutput;
    };
  };
}
