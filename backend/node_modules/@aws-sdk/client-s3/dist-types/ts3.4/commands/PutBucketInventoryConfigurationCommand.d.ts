import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { PutBucketInventoryConfigurationRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketInventoryConfigurationCommandInput
  extends PutBucketInventoryConfigurationRequest {}
export interface PutBucketInventoryConfigurationCommandOutput
  extends __MetadataBearer {}
declare const PutBucketInventoryConfigurationCommand_base: {
  new (
    input: PutBucketInventoryConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketInventoryConfigurationCommandInput,
    PutBucketInventoryConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: PutBucketInventoryConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketInventoryConfigurationCommandInput,
    PutBucketInventoryConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketInventoryConfigurationCommand extends PutBucketInventoryConfigurationCommand_base {
  protected static __types: {
    api: {
      input: PutBucketInventoryConfigurationRequest;
      output: {};
    };
    sdk: {
      input: PutBucketInventoryConfigurationCommandInput;
      output: PutBucketInventoryConfigurationCommandOutput;
    };
  };
}
