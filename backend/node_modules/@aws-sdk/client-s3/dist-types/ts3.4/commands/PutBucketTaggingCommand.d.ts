import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketTaggingRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketTaggingCommandInput extends PutBucketTaggingRequest {}
export interface PutBucketTaggingCommandOutput extends __MetadataBearer {}
declare const PutBucketTaggingCommand_base: {
  new (
    input: PutBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketTaggingCommandInput,
    PutBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: PutBucketTaggingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketTaggingCommandInput,
    PutBucketTaggingCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketTaggingCommand extends PutBucketTaggingCommand_base {
  protected static __types: {
    api: {
      input: PutBucketTaggingRequest;
      output: {};
    };
    sdk: {
      input: PutBucketTaggingCommandInput;
      output: PutBucketTaggingCommandOutput;
    };
  };
}
