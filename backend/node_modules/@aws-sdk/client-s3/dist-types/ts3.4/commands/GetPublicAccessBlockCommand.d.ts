import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetPublicAccessBlockOutput,
  GetPublicAccessBlockRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetPublicAccessBlockCommandInput
  extends GetPublicAccessBlockRequest {}
export interface GetPublicAccessBlockCommandOutput
  extends GetPublicAccessBlockOutput,
    __MetadataBearer {}
declare const GetPublicAccessBlockCommand_base: {
  new (
    input: GetPublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetPublicAccessBlockCommandInput,
    GetPublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: GetPublicAccessBlockCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetPublicAccessBlockCommandInput,
    GetPublicAccessBlockCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetPublicAccessBlockCommand extends GetPublicAccessBlockCommand_base {
  protected static __types: {
    api: {
      input: GetPublicAccessBlockRequest;
      output: GetPublicAccessBlockOutput;
    };
    sdk: {
      input: GetPublicAccessBlockCommandInput;
      output: GetPublicAccessBlockCommandOutput;
    };
  };
}
