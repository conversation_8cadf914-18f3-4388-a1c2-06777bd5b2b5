import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketVersioningRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketVersioningCommandInput
  extends PutBucketVersioningRequest {}
export interface PutBucketVersioningCommandOutput extends __MetadataBearer {}
declare const PutBucketVersioningCommand_base: {
  new (
    input: PutBucketVersioningCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketVersioningCommandInput,
    PutBucketVersioningCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: PutBucketVersioningCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketVersioningCommandInput,
    PutBucketVersioningCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketVersioningCommand extends PutBucketVersioningCommand_base {
  protected static __types: {
    api: {
      input: PutBucketVersioningRequest;
      output: {};
    };
    sdk: {
      input: PutBucketVersioningCommandInput;
      output: PutBucketVersioningCommandOutput;
    };
  };
}
