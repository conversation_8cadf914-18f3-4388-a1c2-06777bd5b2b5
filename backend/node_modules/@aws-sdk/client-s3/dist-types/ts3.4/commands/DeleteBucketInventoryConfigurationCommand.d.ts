import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { DeleteBucketInventoryConfigurationRequest } from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface DeleteBucketInventoryConfigurationCommandInput
  extends DeleteBucketInventoryConfigurationRequest {}
export interface DeleteBucketInventoryConfigurationCommandOutput
  extends __MetadataBearer {}
declare const DeleteBucketInventoryConfigurationCommand_base: {
  new (
    input: DeleteBucketInventoryConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketInventoryConfigurationCommandInput,
    DeleteBucketInventoryConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: DeleteBucketInventoryConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteBucketInventoryConfigurationCommandInput,
    DeleteBucketInventoryConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteBucketInventoryConfigurationCommand extends DeleteBucketInventoryConfigurationCommand_base {
  protected static __types: {
    api: {
      input: DeleteBucketInventoryConfigurationRequest;
      output: {};
    };
    sdk: {
      input: DeleteBucketInventoryConfigurationCommandInput;
      output: DeleteBucketInventoryConfigurationCommandOutput;
    };
  };
}
