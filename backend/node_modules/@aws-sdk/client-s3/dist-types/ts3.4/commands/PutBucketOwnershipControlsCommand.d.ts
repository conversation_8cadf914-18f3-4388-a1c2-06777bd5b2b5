import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { PutBucketOwnershipControlsRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketOwnershipControlsCommandInput
  extends PutBucketOwnershipControlsRequest {}
export interface PutBucketOwnershipControlsCommandOutput
  extends __MetadataBearer {}
declare const PutBucketOwnershipControlsCommand_base: {
  new (
    input: PutBucketOwnershipControlsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketOwnershipControlsCommandInput,
    PutBucketOwnershipControlsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: PutBucketOwnershipControlsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketOwnershipControlsCommandInput,
    PutBucketOwnershipControlsCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketOwnershipControlsCommand extends PutBucketOwnershipControlsCommand_base {
  protected static __types: {
    api: {
      input: PutBucketOwnershipControlsRequest;
      output: {};
    };
    sdk: {
      input: PutBucketOwnershipControlsCommandInput;
      output: PutBucketOwnershipControlsCommandOutput;
    };
  };
}
