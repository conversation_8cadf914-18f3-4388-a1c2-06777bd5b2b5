import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListAccountRolesRequest,
  ListAccountRolesResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSOClientResolvedConfig,
} from "../SSOClient";
export { __MetadataBearer };
export { $Command };
export interface ListAccountRolesCommandInput extends ListAccountRolesRequest {}
export interface ListAccountRolesCommandOutput
  extends ListAccountRolesResponse,
    __MetadataBearer {}
declare const ListAccountRolesCommand_base: {
  new (
    input: ListAccountRolesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAccountRolesCommandInput,
    ListAccountRolesCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: ListAccountRolesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAccountRolesCommandInput,
    ListAccountRolesCommandOutput,
    SSOClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListAccountRolesCommand extends ListAccountRolesCommand_base {
  protected static __types: {
    api: {
      input: ListAccountRolesRequest;
      output: ListAccountRolesResponse;
    };
    sdk: {
      input: ListAccountRolesCommandInput;
      output: ListAccountRolesCommandOutput;
    };
  };
}
